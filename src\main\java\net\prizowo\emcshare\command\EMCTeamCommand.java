package net.prizowo.emcshare.command;

import com.mojang.brigadier.CommandDispatcher;
import com.mojang.brigadier.arguments.StringArgumentType;
import com.mojang.brigadier.context.CommandContext;
import com.mojang.brigadier.exceptions.CommandSyntaxException;
import net.minecraft.ChatFormatting;
import net.minecraft.commands.CommandSourceStack;
import net.minecraft.commands.Commands;
import net.minecraft.commands.arguments.EntityArgument;
import net.minecraft.network.chat.Component;
import net.minecraft.server.level.ServerPlayer;
import net.prizowo.emcshare.capability.SharedEMCCapability;
import net.prizowo.emcshare.data.EMCTeam;
import net.prizowo.emcshare.data.EMCTeamManager;

import java.math.BigInteger;
import java.util.Collection;

public class EMCTeamCommand {
    
    public static void register(CommandDispatcher<CommandSourceStack> dispatcher) {
        dispatcher.register(Commands.literal("emcteam")
            .then(Commands.literal("create")
                .then(Commands.argument("name", StringArgumentType.string())
                    .executes(EMCTeamCommand::createTeam)))
            .then(Commands.literal("delete")
                .executes(EMCTeamCommand::deleteTeam))
            .then(Commands.literal("join")
                .then(Commands.argument("teamName", StringArgumentType.string())
                    .executes(EMCTeamCommand::joinTeam))
                .executes(EMCTeamCommand::joinPendingTeam))
            .then(Commands.literal("leave")
                .executes(EMCTeamCommand::leaveTeam))
            .then(Commands.literal("invite")
                .then(Commands.argument("player", EntityArgument.player())
                    .executes(EMCTeamCommand::invitePlayer)))
            .then(Commands.literal("kick")
                .then(Commands.argument("player", EntityArgument.player())
                    .executes(EMCTeamCommand::kickPlayer)))
            .then(Commands.literal("info")
                .executes(EMCTeamCommand::teamInfo))
            .then(Commands.literal("list")
                .executes(EMCTeamCommand::listTeams))
            .then(Commands.literal("toggle")
                .executes(EMCTeamCommand::toggleSharedEMC))
            .then(Commands.literal("invites")
                .executes(EMCTeamCommand::showInvites))
        );
    }

    private static int createTeam(CommandContext<CommandSourceStack> context) throws CommandSyntaxException {
        ServerPlayer player = context.getSource().getPlayerOrException();
        String teamName = StringArgumentType.getString(context, "name");
        
        EMCTeamManager manager = EMCTeamManager.getInstance();
        if (manager == null) {
            context.getSource().sendFailure(Component.literal("Team manager not available"));
            return 0;
        }
        
        if (manager.isPlayerInTeam(player.getUUID())) {
            context.getSource().sendFailure(Component.literal("You are already in a team. Leave your current team first."));
            return 0;
        }
        
        EMCTeam team = manager.createTeam(teamName, player.getUUID());
        context.getSource().sendSuccess(() -> Component.literal("Created team: " + teamName + " (ID: " + team.getTeamId() + ")")
            .withStyle(ChatFormatting.GREEN), false);
        
        return 1;
    }

    private static int deleteTeam(CommandContext<CommandSourceStack> context) throws CommandSyntaxException {
        ServerPlayer player = context.getSource().getPlayerOrException();
        
        EMCTeamManager manager = EMCTeamManager.getInstance();
        if (manager == null) {
            context.getSource().sendFailure(Component.literal("Team manager not available"));
            return 0;
        }
        
        EMCTeam team = manager.getPlayerTeam(player.getUUID());
        if (team == null) {
            context.getSource().sendFailure(Component.literal("You are not in a team"));
            return 0;
        }
        
        if (!team.isOwner(player.getUUID())) {
            context.getSource().sendFailure(Component.literal("Only team owner can delete the team"));
            return 0;
        }
        
        if (manager.deleteTeam(team.getTeamId(), player.getUUID())) {
            context.getSource().sendSuccess(() -> Component.literal("Team deleted successfully")
                .withStyle(ChatFormatting.GREEN), false);
            return 1;
        } else {
            context.getSource().sendFailure(Component.literal("Failed to delete team"));
            return 0;
        }
    }

    private static int joinTeam(CommandContext<CommandSourceStack> context) throws CommandSyntaxException {
        ServerPlayer player = context.getSource().getPlayerOrException();
        String teamName = StringArgumentType.getString(context, "teamName");

        EMCTeamManager manager = EMCTeamManager.getInstance();
        if (manager == null) {
            context.getSource().sendFailure(Component.literal("Team manager not available"));
            return 0;
        }

        if (manager.joinTeam(teamName, player.getUUID())) {
            EMCTeam team = manager.getTeamByName(teamName);
            if (team == null) {
                team = manager.getTeam(teamName); // fallback to ID lookup
            }
            final String displayName = team != null ? team.getTeamName() : teamName;
            context.getSource().sendSuccess(() -> Component.literal("Joined team: " + displayName)
                .withStyle(ChatFormatting.GREEN), false);
            return 1;
        } else {
            context.getSource().sendFailure(Component.literal("Failed to join team '" + teamName + "'. Team may not exist or you may not have an invitation."));
            return 0;
        }
    }

    private static int joinPendingTeam(CommandContext<CommandSourceStack> context) throws CommandSyntaxException {
        ServerPlayer player = context.getSource().getPlayerOrException();

        EMCTeamManager manager = EMCTeamManager.getInstance();
        if (manager == null) {
            context.getSource().sendFailure(Component.literal("Team manager not available"));
            return 0;
        }

        String pendingTeamName = manager.getPendingInvite(player.getUUID());
        if (pendingTeamName == null) {
            context.getSource().sendFailure(Component.literal("You have no pending team invitations. Use '/emcteam join <teamname>' to join a specific team."));
            return 0;
        }

        if (manager.joinTeam(pendingTeamName, player.getUUID())) {
            context.getSource().sendSuccess(() -> Component.literal("Joined team: " + pendingTeamName)
                .withStyle(ChatFormatting.GREEN), false);
            return 1;
        } else {
            context.getSource().sendFailure(Component.literal("Failed to join team. The team may no longer exist."));
            return 0;
        }
    }

    private static int leaveTeam(CommandContext<CommandSourceStack> context) throws CommandSyntaxException {
        ServerPlayer player = context.getSource().getPlayerOrException();
        
        EMCTeamManager manager = EMCTeamManager.getInstance();
        if (manager == null) {
            context.getSource().sendFailure(Component.literal("Team manager not available"));
            return 0;
        }
        
        if (manager.leaveTeam(player.getUUID())) {
            context.getSource().sendSuccess(() -> Component.literal("Left team successfully")
                .withStyle(ChatFormatting.GREEN), false);
            return 1;
        } else {
            context.getSource().sendFailure(Component.literal("You are not in a team"));
            return 0;
        }
    }

    private static int invitePlayer(CommandContext<CommandSourceStack> context) throws CommandSyntaxException {
        ServerPlayer player = context.getSource().getPlayerOrException();
        ServerPlayer target = EntityArgument.getPlayer(context, "player");
        
        EMCTeamManager manager = EMCTeamManager.getInstance();
        if (manager == null) {
            context.getSource().sendFailure(Component.literal("Team manager not available"));
            return 0;
        }
        
        EMCTeam team = manager.getPlayerTeam(player.getUUID());
        if (team == null) {
            context.getSource().sendFailure(Component.literal("You are not in a team"));
            return 0;
        }
        
        if (!team.isOwner(player.getUUID())) {
            context.getSource().sendFailure(Component.literal("Only team owner can invite players"));
            return 0;
        }
        
        if (manager.isPlayerInTeam(target.getUUID())) {
            context.getSource().sendFailure(Component.literal("Player is already in a team"));
            return 0;
        }
        
        // Add pending invitation
        manager.addPendingInvite(target.getUUID(), team.getTeamName());

        // Send invitation to target player
        target.sendSystemMessage(Component.literal("You have been invited to join team: " + team.getTeamName())
            .withStyle(ChatFormatting.YELLOW));
        target.sendSystemMessage(Component.literal("Use '/emcteam join' to accept or '/emcteam join " + team.getTeamName() + "' to join directly")
            .withStyle(ChatFormatting.GOLD));

        context.getSource().sendSuccess(() -> Component.literal("Invitation sent to " + target.getName().getString())
            .withStyle(ChatFormatting.GREEN), false);
        
        return 1;
    }

    private static int kickPlayer(CommandContext<CommandSourceStack> context) throws CommandSyntaxException {
        ServerPlayer player = context.getSource().getPlayerOrException();
        ServerPlayer target = EntityArgument.getPlayer(context, "player");

        EMCTeamManager manager = EMCTeamManager.getInstance();
        if (manager == null) {
            context.getSource().sendFailure(Component.literal("Team manager not available"));
            return 0;
        }

        EMCTeam team = manager.getPlayerTeam(player.getUUID());
        if (team == null) {
            context.getSource().sendFailure(Component.literal("You are not in a team"));
            return 0;
        }

        if (!team.isOwner(player.getUUID())) {
            context.getSource().sendFailure(Component.literal("Only team owner can kick players"));
            return 0;
        }

        if (!team.isMember(target.getUUID())) {
            context.getSource().sendFailure(Component.literal("Player is not in your team"));
            return 0;
        }

        if (team.isOwner(target.getUUID())) {
            context.getSource().sendFailure(Component.literal("Cannot kick team owner"));
            return 0;
        }

        if (team.removeMember(target.getUUID())) {
            EMCTeamManager.saveTeams();
            target.sendSystemMessage(Component.literal("You have been kicked from team: " + team.getTeamName())
                .withStyle(ChatFormatting.RED));
            context.getSource().sendSuccess(() -> Component.literal("Kicked " + target.getName().getString() + " from team")
                .withStyle(ChatFormatting.GREEN), false);
            return 1;
        } else {
            context.getSource().sendFailure(Component.literal("Failed to kick player"));
            return 0;
        }
    }

    private static int teamInfo(CommandContext<CommandSourceStack> context) throws CommandSyntaxException {
        ServerPlayer player = context.getSource().getPlayerOrException();

        EMCTeamManager manager = EMCTeamManager.getInstance();
        if (manager == null) {
            context.getSource().sendFailure(Component.literal("Team manager not available"));
            return 0;
        }

        EMCTeam team = manager.getPlayerTeam(player.getUUID());
        if (team == null) {
            context.getSource().sendFailure(Component.literal("You are not in a team"));
            return 0;
        }

        context.getSource().sendSuccess(() -> Component.literal("=== Team Info ===").withStyle(ChatFormatting.GOLD), false);
        context.getSource().sendSuccess(() -> Component.literal("Name: " + team.getTeamName()).withStyle(ChatFormatting.YELLOW), false);
        context.getSource().sendSuccess(() -> Component.literal("ID: " + team.getTeamId()).withStyle(ChatFormatting.GRAY), false);
        context.getSource().sendSuccess(() -> Component.literal("Members: " + team.getMemberCount()).withStyle(ChatFormatting.AQUA), false);

        // Calculate and display total team EMC
        player.getCapability(SharedEMCCapability.SHARED_EMC_CAPABILITY).ifPresent(cap -> {
            if (cap instanceof SharedEMCCapability.SharedEMCProvider provider) {
                BigInteger totalEMC = provider.calculateTeamTotalEMC(team);
                context.getSource().sendSuccess(() -> Component.literal("Total Team EMC: " + totalEMC.toString()).withStyle(ChatFormatting.GREEN), false);
            }
        });

        // Check if player is using shared EMC
        player.getCapability(SharedEMCCapability.SHARED_EMC_CAPABILITY).ifPresent(cap -> {
            context.getSource().sendSuccess(() -> Component.literal("Using Shared EMC: " + (cap.isUsingSharedEMC() ? "Yes" : "No"))
                .withStyle(cap.isUsingSharedEMC() ? ChatFormatting.GREEN : ChatFormatting.RED), false);
        });

        return 1;
    }

    private static int listTeams(CommandContext<CommandSourceStack> context) {
        EMCTeamManager manager = EMCTeamManager.getInstance();
        if (manager == null) {
            context.getSource().sendFailure(Component.literal("Team manager not available"));
            return 0;
        }

        Collection<EMCTeam> teams = manager.getAllTeams();
        if (teams.isEmpty()) {
            context.getSource().sendSuccess(() -> Component.literal("No teams exist").withStyle(ChatFormatting.YELLOW), false);
            return 1;
        }

        context.getSource().sendSuccess(() -> Component.literal("=== All Teams ===").withStyle(ChatFormatting.GOLD), false);
        for (EMCTeam team : teams) {
            context.getSource().sendSuccess(() -> Component.literal(team.getTeamName() + " (" + team.getTeamId() + ") - " +
                team.getMemberCount() + " members").withStyle(ChatFormatting.AQUA), false);
        }

        return 1;
    }

    private static int toggleSharedEMC(CommandContext<CommandSourceStack> context) throws CommandSyntaxException {
        ServerPlayer player = context.getSource().getPlayerOrException();

        EMCTeamManager manager = EMCTeamManager.getInstance();
        if (manager == null) {
            context.getSource().sendFailure(Component.literal("Team manager not available"));
            return 0;
        }

        if (!manager.isPlayerInTeam(player.getUUID())) {
            context.getSource().sendFailure(Component.literal("You must be in a team to use shared EMC"));
            return 0;
        }

        player.getCapability(SharedEMCCapability.SHARED_EMC_CAPABILITY).ifPresent(cap -> {
            boolean newState = !cap.isUsingSharedEMC();
            cap.setUsingSharedEMC(newState);
            context.getSource().sendSuccess(() -> Component.literal("Shared EMC " + (newState ? "enabled" : "disabled"))
                .withStyle(newState ? ChatFormatting.GREEN : ChatFormatting.RED), false);
        });

        return 1;
    }

    private static int showInvites(CommandContext<CommandSourceStack> context) throws CommandSyntaxException {
        ServerPlayer player = context.getSource().getPlayerOrException();

        EMCTeamManager manager = EMCTeamManager.getInstance();
        if (manager == null) {
            context.getSource().sendFailure(Component.literal("Team manager not available"));
            return 0;
        }

        String pendingTeamName = manager.getPendingInvite(player.getUUID());
        if (pendingTeamName == null) {
            context.getSource().sendSuccess(() -> Component.literal("You have no pending team invitations")
                .withStyle(ChatFormatting.YELLOW), false);
            return 1;
        }

        context.getSource().sendSuccess(() -> Component.literal("=== Pending Invitations ===").withStyle(ChatFormatting.GOLD), false);
        context.getSource().sendSuccess(() -> Component.literal("Team: " + pendingTeamName).withStyle(ChatFormatting.AQUA), false);
        context.getSource().sendSuccess(() -> Component.literal("Use '/emcteam join' to accept or '/emcteam join " + pendingTeamName + "' to join directly")
            .withStyle(ChatFormatting.GREEN), false);

        return 1;
    }
}
