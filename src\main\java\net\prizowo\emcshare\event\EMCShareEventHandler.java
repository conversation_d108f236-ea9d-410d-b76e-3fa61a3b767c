package net.prizowo.emcshare.event;

import moze_intel.projecte.api.event.PlayerAttemptCondenserSetEvent;
import moze_intel.projecte.api.event.PlayerAttemptLearnEvent;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.player.Player;
import net.minecraftforge.event.TickEvent;
import net.minecraftforge.event.entity.player.PlayerEvent;
import net.minecraftforge.event.entity.player.PlayerContainerEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;
import net.prizowo.emcshare.EMCShareMod;
import net.prizowo.emcshare.capability.SharedEMCCapability;
import net.prizowo.emcshare.data.EMCTeam;
import net.prizowo.emcshare.data.EMCTeamManager;
import net.prizowo.emcshare.network.PacketHandler;
import net.prizowo.emcshare.network.packet.SyncSharedEMCPacket;

import java.math.BigInteger;

@Mod.EventBusSubscriber(modid = EMCShareMod.MODID)
public class EMCShareEventHandler {

    private static int tickCounter = 0;

    @SubscribeEvent
    public static void onPlayerClone(PlayerEvent.Clone event) {
        if (event.isWasDeath()) {
            // Copy shared EMC capability on death
            Entity original = event.getOriginal();
            Entity newPlayer = event.getEntity();
            
            original.getCapability(SharedEMCCapability.SHARED_EMC_CAPABILITY).ifPresent(oldCap -> {
                newPlayer.getCapability(SharedEMCCapability.SHARED_EMC_CAPABILITY).ifPresent(newCap -> {
                    newCap.deserializeNBT(oldCap.serializeNBT());
                });
            });
        }
    }

    @SubscribeEvent
    public static void onPlayerLoggedIn(PlayerEvent.PlayerLoggedInEvent event) {
        if (event.getEntity() instanceof ServerPlayer player) {
            // Sync team data and EMC immediately on login
            EMCShareMod.LOGGER.info("Player {} logged in", player.getName().getString());

            // Force sync shared EMC data for this player
            forceSyncPlayerEMC(player);
        }
    }

    @SubscribeEvent
    public static void onPlayerLoggedOut(PlayerEvent.PlayerLoggedOutEvent event) {
        if (event.getEntity() instanceof ServerPlayer player) {
            // Clean up or save any necessary data
            EMCShareMod.LOGGER.info("Player {} logged out", player.getName().getString());
        }
    }

    @SubscribeEvent
    public static void onPlayerChangedDimension(PlayerEvent.PlayerChangedDimensionEvent event) {
        // Ensure shared EMC capability persists across dimensions
        if (event.getEntity() instanceof ServerPlayer player) {
            EMCShareMod.LOGGER.debug("Player {} changed dimension", player.getName().getString());
        }
    }

    // Hook into ProjectE events to potentially modify EMC behavior
    @SubscribeEvent
    public static void onPlayerAttemptLearn(PlayerAttemptLearnEvent event) {
        // This event is fired when a player attempts to learn an item
        // We can potentially modify this behavior for team members
        Player player = event.getPlayer();
        
        player.getCapability(SharedEMCCapability.SHARED_EMC_CAPABILITY).ifPresent(cap -> {
            if (cap.isUsingSharedEMC()) {
                EMCTeamManager manager = EMCTeamManager.getInstance();
                if (manager != null && manager.isPlayerInTeam(player.getUUID())) {
                    // Could implement shared learning here if desired
                    EMCShareMod.LOGGER.debug("Player {} learning item while in shared EMC mode", 
                        player.getName().getString());
                }
            }
        });
    }

    @SubscribeEvent
    public static void onPlayerAttemptCondenserSet(PlayerAttemptCondenserSetEvent event) {
        // This event is fired when a player attempts to set a condenser target
        Player player = event.getPlayer();
        
        player.getCapability(SharedEMCCapability.SHARED_EMC_CAPABILITY).ifPresent(cap -> {
            if (cap.isUsingSharedEMC()) {
                EMCTeamManager manager = EMCTeamManager.getInstance();
                if (manager != null && manager.isPlayerInTeam(player.getUUID())) {
                    EMCShareMod.LOGGER.debug("Player {} setting condenser while in shared EMC mode", 
                        player.getName().getString());
                }
            }
        });
    }

    @SubscribeEvent
    public static void onPlayerContainerOpen(PlayerContainerEvent.Open event) {
        if (event.getEntity() instanceof ServerPlayer player) {
            // Check if this is a transmutation table container
            if (event.getContainer().getClass().getName().contains("TransmutationContainer")) {
                // Force sync EMC when opening transmutation table
                forceSyncPlayerEMC(player);
            }
        }
    }

    @SubscribeEvent
    public static void onServerTick(TickEvent.ServerTickEvent event) {
        if (event.phase == TickEvent.Phase.END) {
            tickCounter++;
            // Sync shared EMC every 60 ticks (3 seconds) - reduced frequency since we have immediate sync
            if (tickCounter % 60 == 0) {
                syncSharedEMCToAllPlayers();
            }
        }
    }

    /**
     * Force sync EMC data for a specific player
     */
    public static void forceSyncPlayerEMC(ServerPlayer player) {
        player.getCapability(SharedEMCCapability.SHARED_EMC_CAPABILITY).ifPresent(cap -> {
            if (cap.isUsingSharedEMC()) {
                EMCTeamManager manager = EMCTeamManager.getInstance();
                if (manager != null) {
                    EMCTeam team = manager.getPlayerTeam(player.getUUID());
                    if (team != null) {
                        BigInteger totalEMC = cap.getAvailableEMC(player);
                        SyncSharedEMCPacket packet = new SyncSharedEMCPacket(true, totalEMC, team.getTeamName(), team.getMemberCount());
                        PacketHandler.sendToPlayer(packet, player);
                    }
                }
            }
        });
    }

    private static void syncSharedEMCToAllPlayers() {
        EMCTeamManager manager = EMCTeamManager.getInstance();
        if (manager == null) return;

        try {
            net.minecraft.server.MinecraftServer server = net.minecraftforge.server.ServerLifecycleHooks.getCurrentServer();
            if (server != null) {
                for (net.minecraft.server.level.ServerPlayer player : server.getPlayerList().getPlayers()) {
                    forceSyncPlayerEMC(player);
                }
            }
        } catch (Exception e) {
            EMCShareMod.LOGGER.error("Error syncing shared EMC", e);
        }
    }
}
