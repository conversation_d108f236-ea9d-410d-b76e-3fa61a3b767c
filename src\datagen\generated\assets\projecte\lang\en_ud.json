{"advancements.projecte.alch_bag": "¡ǝᵷɐɹoʇs ʇǝʞɔoԀ", "advancements.projecte.alch_bag.description": "˙ʇǝʞɔod ɹnoʎ uᴉ 'ʇsǝɥɔ ꞁɐɔᴉɯǝɥɔꞁɐ uɐ ɟo sɹǝpuoʍ ǝɥʇ ꞁꞁⱯ", "advancements.projecte.alch_chest": "¡ǝpɐɹᵷd∩ ǝᵷɐɹoʇS", "advancements.projecte.alch_chest.description": "˙ǝpɐɹᵷdn ʇsǝɥɔ „ǝꞁʇʇᴉꞁ„ Ɐ", "advancements.projecte.collector": "¡uns ǝɥʇ ɟo ɹǝʍod ǝɥ⟘", "advancements.projecte.collector.description": "˙suᴉᵷǝq unɟ ǝɥʇ ʍoN", "advancements.projecte.condenser": "¡pꞁɹoʍ ǝɥʇ ǝsuǝpuoƆ", "advancements.projecte.condenser.description": "¡SᗡNOWⱯIᗡ ƎᴚOW", "advancements.projecte.dark_matter": "˙sɹǝʇʇɐW ʇɐɥʇ ꞁꞁⱯ", "advancements.projecte.dark_matter.description": "˙˙˙˙pɹᴉǝʍ ˙˙˙sʞooꞁ ʇI", "advancements.projecte.dark_matter_block": "¡sɹǝʇʇɐW ʇɐɥʇ ʞɔoꞁq Ɐ", "advancements.projecte.dark_matter_block.description": "˙ɐǝpᴉ pooᵷ ɐ s,ʇɐɥʇ ǝsnɐɔǝᗺ ˙ɹǝɥʇǝᵷoʇ ɹǝʇʇɐɯ ᵷuᴉɟɟnʇS", "advancements.projecte.dark_matter_furnace": "¡ɹǝʇʇɐɯ ʇoH", "advancements.projecte.dark_matter_furnace.description": "˙ɹǝʇʇɐɯ ʞɹɐp ɯoɹɟ ǝpɐɯ uǝɥʍ ɹǝʇʇǝq uǝʌǝ sᴉ ǝɔɐuɹnɟ Ɐ", "advancements.projecte.dark_matter_pickaxe": "ɹǝʇʇɐW uo ɹǝʇʇɐW ᵷuᴉs∩", "advancements.projecte.dark_matter_pickaxe.description": "¿ʇou ʎɥʍ ǝsnɐɔǝᗺ", "advancements.projecte.description": "¿ǝɔɹǝɯɯoƆ ʇuǝpuodsǝɹɹoƆ", "advancements.projecte.klein_star": "sǝᴉɹǝʇʇɐᗺ ƆWƎ", "advancements.projecte.klein_star.description": "˙ʎɐp ʎuᴉɐɹ ɐ ɹoɟ ƆWƎ ᵷuᴉɹoʇS", "advancements.projecte.klein_star_big": "sǝᴉɹǝʇʇɐᗺ ƆWƎ ⅁Iᗺ", "advancements.projecte.klein_star_big.description": "˙ʇǝʞɔod ɹnoʎ uᴉ ǝsɹǝʌᴉun ǝɥʇ ᵷuᴉpꞁoH", "advancements.projecte.philo_stone": "¡puǝᴉɹɟ ʇsǝq s,ʇsᴉɯǝɥɔꞁɐ uⱯ", "advancements.projecte.philo_stone.description": "ǝuoʇs s,ɹǝɥdosoꞁᴉɥd ɐ ʇɟɐɹƆ ¡pǝʇɹɐʇs sᵷuᴉɥʇ ʇǝᵷ s,ʇǝꞀ", "advancements.projecte.red_matter": "¡ɹǝʇʇɐW ɹǝʇʇǝq uǝʌƎ", "advancements.projecte.red_matter.description": "˙uǝʞoɹq ǝq ʎɐɯ ɯnnuᴉʇuoɔ ǝɯᴉʇ ǝɔɐds ǝɥ⟘", "advancements.projecte.red_matter_block": "¡ʎuᴉɥs puɐ pǝᴚ", "advancements.projecte.red_matter_block.description": "¡ǝɹǝɥʍǝɯos ᵷuᴉʇʇǝᵷ ǝɹ,noʎ ʍoN", "advancements.projecte.red_matter_furnace": "¡ɹǝʇʇɐɯ ɹǝʇʇoɥ uǝʌƎ", "advancements.projecte.red_matter_furnace.description": "˙ʇsɐɟ sᴉ ᵷuᴉɥʇ ʇɐɥʇ 'ʍoM", "advancements.projecte.red_matter_pickaxe": "¿ǝɟɐs ᵷuᴉɥʇ sᴉɥʇ sI", "advancements.projecte.red_matter_pickaxe.description": "˙ʇou ʎꞁqɐqoɹԀ", "advancements.projecte.relay": "¡sɹǝʍoꞁɟ ɹǝʍoԀ", "advancements.projecte.relay.description": "˙ɹǝʍod ǝɹoɯ uǝʌǝ ɹoɟ ɹǝɥʇǝᵷoʇ sɹoʇɔǝꞁꞁoɔ ᵷuᴉʞuᴉꞀ", "advancements.projecte.transmutation_table": "¡ʇɐɥʇ oʇuᴉ sᴉɥʇ ǝʇnɯsuɐɹ⟘", "advancements.projecte.transmutation_table.description": "˙ᵷuᴉɥʇʎɹǝʌǝ ɟo (puǝ puɐ) ᵷuᴉuuᴉᵷǝq ǝɥ⟘", "advancements.projecte.transmutation_tablet": "¡oᵷ ǝɥʇ uo uoᴉʇɐʇnɯsuɐɹ⟘", "advancements.projecte.transmutation_tablet.description": "˙ɹǝʇʇǝq ʇǝᵷ ʇ,upꞁnoɔ sᵷuᴉɥʇ ʇɥᵷnoɥʇ noʎ uǝɥʇ puⱯ", "block.projecte.aeternalis_fuel_block": "ʞɔoꞁᗺ ꞁǝnℲ sᴉꞁɐuɹǝʇǝⱯ", "block.projecte.alchemical_chest": "ʇsǝɥƆ ꞁɐɔᴉɯǝɥɔꞁⱯ", "block.projecte.alchemical_coal_block": "ʞɔoꞁᗺ ꞁɐoƆ ꞁɐɔᴉɯǝɥɔꞁⱯ", "block.projecte.collector_mk1": "⥝ꞰW ɹoʇɔǝꞁꞁoƆ ʎᵷɹǝuƎ", "block.projecte.collector_mk2": "ᘔꞰW ɹoʇɔǝꞁꞁoƆ ʎᵷɹǝuƎ", "block.projecte.collector_mk3": "ƐꞰW ɹoʇɔǝꞁꞁoƆ ʎᵷɹǝuƎ", "block.projecte.condenser_mk1": "ɹǝsuǝpuoƆ ʎᵷɹǝuƎ", "block.projecte.condenser_mk2": "ᘔꞰW ɹǝsuǝpuoƆ ʎᵷɹǝuƎ", "block.projecte.dark_matter_block": "ʞɔoꞁᗺ ɹǝʇʇɐW ʞɹɐᗡ", "block.projecte.dm_furnace": "ǝɔɐuɹnℲ ɹǝʇʇɐW ʞɹɐᗡ", "block.projecte.dm_pedestal": "ꞁɐʇsǝpǝԀ ɹǝʇʇɐW ʞɹɐᗡ", "block.projecte.interdiction_torch": "ɥɔɹo⟘ uoᴉʇɔᴉpɹǝʇuI", "block.projecte.mobius_fuel_block": "ʞɔoꞁᗺ ꞁǝnℲ snᴉqoW", "block.projecte.nova_cataclysm": "ɯsʎꞁɔɐʇɐƆ ɐʌoN", "block.projecte.nova_catalyst": "ʇsʎꞁɐʇɐƆ ɐʌoN", "block.projecte.red_matter_block": "ʞɔoꞁᗺ ɹǝʇʇɐW pǝᴚ", "block.projecte.relay_mk1": "⥝ꞰW ʎɐꞁǝᴚ ɹǝʇʇɐW-ᴉʇuⱯ", "block.projecte.relay_mk2": "ᘔꞰW ʎɐꞁǝᴚ ɹǝʇʇɐW-ᴉʇuⱯ", "block.projecte.relay_mk3": "ƐꞰW ʎɐꞁǝᴚ ɹǝʇʇɐW-ᴉʇuⱯ", "block.projecte.rm_furnace": "ǝɔɐuɹnℲ ɹǝʇʇɐW pǝᴚ", "block.projecte.transmutation_table": "ǝꞁqɐ⟘ uoᴉʇɐʇnɯsuɐɹ⟘", "command.projecte.clear_knowledge.notify": "¡%s ʎq pǝɹɐǝꞁɔ sɐʍ ǝᵷpǝꞁʍouʞ uoᴉʇɐʇnɯsuɐɹʇ ɹno⅄", "command.projecte.clear_knowledge.success": "%s :ɹoɟ ǝᵷpǝꞁʍouʞ pǝɹɐǝꞁƆ", "command.projecte.dump_missing_emc.multiple_missing": "˙ᵷoꞁ ʇuǝᴉꞁɔ oʇ ᵷuᴉʇuᴉɹd 'ǝnꞁɐʌ ƆWƎ uɐ ᵷuᴉssᴉɯ ǝɹɐ sɯǝʇI %s", "command.projecte.dump_missing_emc.none_missing": "˙ǝnꞁɐʌ ƆWƎ uɐ ǝʌɐɥ sɯǝʇI ꞁꞁⱯ", "command.projecte.dump_missing_emc.one_missing": "˙ᵷoꞁ ɹǝʌɹǝs oʇ ᵷuᴉʇuᴉɹd 'ǝnꞁɐʌ ƆWƎ uɐ ᵷuᴉssᴉɯ sᴉ ɯǝʇI ǝuO", "command.projecte.emc.add.success": "˙%2$s oʇ ƆWƎ %1$s pǝppⱯ", "command.projecte.emc.get.success": "˙ƆWƎ %2$s sɐɥ %1$s", "command.projecte.emc.invalid": "˙ɹǝᵷǝʇuᴉ ǝʌᴉʇᴉsod ɐ ǝq ʇsnɯ ʇᴉ 'pᴉꞁɐʌuᴉ sᴉ „%s„ ǝnꞁɐʌ ǝɥ⟘", "command.projecte.emc.invalid_item": "¡punoɟ ʇou sɐʍ „%s„ ᵷɐʇ ɹo ɯǝʇᴉ ǝɥ⟘ :ɹoɹɹƎ", "command.projecte.emc.negative": "˙ǝʌᴉʇɐᵷǝu ƆWƎ ɹᴉǝɥʇ ǝʞɐɯ pꞁnoʍ sᴉɥʇ sɐ %2$s ɯoɹɟ ƆWƎ %1$s ǝʌoɯǝɹ ʇouuɐƆ", "command.projecte.emc.no_item": "ǝᵷuɐɥɔ oʇ ᵷɐʇ ɹo ɯǝʇᴉ uɐ ǝʌᴉᵷ ǝsɐǝꞁԀ", "command.projecte.emc.remove.success": "˙%2$s ɯoɹɟ ƆWƎ %1$s pǝʌoɯǝᴚ", "command.projecte.emc.set.success": "˙%2$s oʇ %1$s ɟo ƆWƎ ǝɥʇ ʇǝS", "command.projecte.emc.test.fail": "˙%2$s ǝʌoɯǝɹ oʇ ƆWƎ ɥᵷnouǝ ǝʌɐɥ ʇou sǝop %1$s", "command.projecte.emc.test.success": "˙%2$s ǝʌoɯǝɹ oʇ ƆWƎ ɥᵷnouǝ ǝʌɐɥ sǝop %1$s", "command.projecte.knowledge.clear.fail": "˙ɹɐǝꞁɔ oʇ ǝᵷpǝꞁʍouʞ ʎuɐ ǝʌɐɥ ʇou sǝop %s", "command.projecte.knowledge.clear.success": "˙%s ɟo ǝᵷpǝꞁʍouʞ ǝɥʇ pǝɹɐǝꞁɔ ʎꞁꞁnɟssǝɔɔnS", "command.projecte.knowledge.invalid": "˙pǝuɹɐǝꞁ ǝq ʇouuɐɔ puɐ 'ǝnꞁɐʌ ƆWƎ uɐ ǝʌɐɥ  ʇou sǝop „%s„ ɯǝʇᴉ ǝɥ⟘", "command.projecte.knowledge.learn.fail": "˙%2$s ɟo ǝᵷpǝꞁʍouʞ sɐɥ ʎpɐǝɹꞁɐ %1$s", "command.projecte.knowledge.learn.success": "˙%2$s pǝuɹɐǝꞁ ʎꞁꞁnɟssǝɔɔns sɐɥ %1$s", "command.projecte.knowledge.test.fail": "˙%2$s ɟo ǝᵷpǝꞁʍouʞ ǝʌɐɥ ʇou sǝop %1$s", "command.projecte.knowledge.test.success": "˙%2$s ɟo ǝᵷpǝꞁʍouʞ sɐɥ %1$s", "command.projecte.knowledge.unlearn.fail": "˙%2$s ɟo ǝᵷpǝꞁʍouʞ ǝʌɐɥ ʇou sǝop %1$s", "command.projecte.knowledge.unlearn.success": "˙%2$s pǝuɹɐǝꞁun ʎꞁꞁnɟssǝɔɔns sɐɥ %1$s", "command.projecte.provider.fail": "˙%s ɹoɟ ɹǝpᴉʌoɹd ʇǝᵷ oʇ pǝꞁᴉɐℲ", "command.projecte.reload.notice": "˙ǝʇǝꞁdɯoɔ ǝɹɐ sǝᵷuɐɥɔ ꞁꞁɐ uǝɥʍ „pɐoꞁǝɹ/„ ǝsn ɹo ʇɹɐʇsǝᴚ", "command.projecte.remove.success": "˙%s ɹoɟ ǝnꞁɐʌ ƆWƎ pǝʌoɯǝᴚ", "command.projecte.reset.success": "˙%s ɹoɟ ǝnꞁɐʌ ƆWƎ ʇǝsǝᴚ", "command.projecte.set.success": "¡%2$s oʇ %1$s ɹoɟ ǝnꞁɐʌ ƆWƎ ʇǝS", "command.projecte.showbag.named": "(%2$s) %1$s", "command.projecte.showbag.offline.notfound": "/ɐʇɐpɹǝʎɐꞁd uᴉ punoɟ ʇou %s ᗡI∩∩", "config.jade.plugin_projecte.emc_provider": "ɹǝpᴉʌoɹԀ ƆWƎ", "curios.identifier.klein_star": "ɹɐʇS uᴉǝꞁꞰ", "divining_rod.projecte.avg_emc": "%2$s :sʞɔoꞁq %1$s ɹoɟ ƆWƎ ǝᵷɐɹǝʌⱯ", "divining_rod.projecte.max_emc": "%s :ƆWƎ xɐW", "divining_rod.projecte.range.16": "ƐxƐx9⥝", "divining_rod.projecte.range.3": "ƐxƐxƐ", "divining_rod.projecte.range.64": "ƐxƐx߈9", "divining_rod.projecte.second_max": "%s :ƆWƎ xɐW puoɔǝS", "divining_rod.projecte.third_max": "%s :ƆWƎ xɐW pɹᴉɥ⟘", "emc.projecte.emc": "ƆWƎ %s", "emc.projecte.has_knowledge": "pǝuɹɐǝꞀ", "emc.projecte.max_gen_rate": "s/ƆWƎ %s :ǝʇɐᴚ uoᴉʇɐɹǝuǝ⅁ ɯnɯᴉxɐW", "emc.projecte.max_output_rate": "s/ƆWƎ %s :ǝʇɐᴚ ʇndʇnO ɯnɯᴉxɐW", "emc.projecte.max_storage": "ƆWƎ %s :ǝᵷɐɹoʇS ɯnɯᴉxɐW", "emc.projecte.no_knowledge": "pǝuɹɐǝꞁu∩", "emc.projecte.postfix.0": "uoᴉꞁꞁᴉɹ⟘ %s", "emc.projecte.postfix.1": "uoᴉꞁꞁᴉɹpɐnꝹ %s", "emc.projecte.postfix.10": "uoᴉꞁꞁᴉɔǝpǝɹ⟘ %s", "emc.projecte.postfix.11": "uoᴉꞁꞁᴉɔǝpɹonʇʇɐnꝹ %s", "emc.projecte.postfix.12": "uoᴉꞁꞁᴉɔǝpuᴉnꝹ %s", "emc.projecte.postfix.13": "uoᴉꞁꞁᴉɔǝpxǝS %s", "emc.projecte.postfix.14": "uoᴉꞁꞁᴉɔǝpuǝʇdǝS %s", "emc.projecte.postfix.15": "uoᴉꞁꞁᴉɔǝpoʇɔO %s", "emc.projecte.postfix.16": "uoᴉꞁꞁᴉɔǝpɯǝʌoN %s", "emc.projecte.postfix.17": "uoᴉꞁꞁᴉʇuᴉᵷᴉΛ %s", "emc.projecte.postfix.2": "uoᴉꞁꞁᴉʇuᴉnꝹ %s", "emc.projecte.postfix.3": "uoᴉꞁꞁᴉʇxǝS %s", "emc.projecte.postfix.4": "uoᴉꞁꞁᴉʇdǝS %s", "emc.projecte.postfix.5": "uoᴉꞁꞁᴉʇɔO %s", "emc.projecte.postfix.6": "uoᴉꞁꞁᴉuoN %s", "emc.projecte.postfix.7": "uoᴉꞁꞁᴉɔǝᗡ %s", "emc.projecte.postfix.8": "uoᴉꞁꞁᴉɔǝpu∩ %s", "emc.projecte.postfix.9": "uoᴉꞁꞁᴉɔǝponᗡ %s", "emc.projecte.stored": "%s :ƆWƎ pǝɹoʇS", "emc.projecte.too_much": "HƆ∩W OO⟘ ⅄ⱯM", "emc.projecte.tooltip": "%s :ƆWƎ", "emc.projecte.tooltip.stack": "%s :ƆWƎ ʞɔɐʇS", "emc.projecte.tooltip.stack.with_sell": "(%2$s) %1$s :ƆWƎ ʞɔɐʇS", "emc.projecte.tooltip.with_sell": "(%2$s) %1$s :ƆWƎ", "entity.projecte.fire_projectile": "ꞁꞁɐqǝɹᴉℲ", "entity.projecte.homing_arrow": "ʍoɹɹⱯ ᵷuᴉɯoH", "entity.projecte.lava_projectile": "qɹO ɐʌɐꞀ", "entity.projecte.lens_projectile": "suǝꞀ ǝʌᴉsoꞁdxƎ", "entity.projecte.mob_randomizer": "qɹO ɹǝzᴉɯopuɐᴚ", "entity.projecte.nova_cataclysm_primed": "ɯsʎꞁɔɐʇɐƆ ɐʌoN pǝɯᴉɹԀ", "entity.projecte.nova_catalyst_primed": "ʇsʎꞁɐʇɐƆ ɐʌoN pǝɯᴉɹԀ", "entity.projecte.swrg_projectile": "ǝꞁᴉʇɔǝɾoɹԀ ⅁ᴚMS", "entity.projecte.water_projectile": "qɹO ɹǝʇɐM", "gem.projecte.activate": "sǝᴉʇᴉꞁᴉqⱯ ǝʌᴉsuǝɟɟO ɹoɯɹⱯ ɯǝ⅁ pǝʇɐʌᴉʇɔⱯ", "gem.projecte.deactivate": "sǝᴉʇᴉꞁᴉqⱯ ǝʌᴉsuǝɟɟO ɹoɯɹⱯ ɯǝ⅁ pǝʇɐʌᴉʇɔɐǝᗡ", "gem.projecte.disabled": "ᗡƎꞀᗺⱯSIᗡ", "gem.projecte.enabled": "ᗡƎꞀᗺⱯNƎ", "gem.projecte.lore.chest": "ɹoɯɹⱯ ꞁɐuɹǝɟuI", "gem.projecte.lore.feet": "sʇooᗺ ǝuɐɔᴉɹɹnH", "gem.projecte.lore.helm": "ʇǝɯꞁǝH ssʎqⱯ", "gem.projecte.lore.legs": "sǝʌɐǝɹ⅁ ʎʇᴉʌɐɹ⅁", "gem.projecte.night_vision": "%s :uoᴉsᴉΛ ʇɥᵷᴉN", "gem.projecte.night_vision.prompt": "uoᴉsᴉΛ ʇɥᵷᴉN ǝꞁᵷᵷoʇ oʇ %s ssǝɹԀ", "gem.projecte.step_assist": "%s :ʇsᴉssⱯ dǝʇS", "gem.projecte.step_assist.prompt": "ʇsᴉssⱯ dǝʇS ǝꞁᵷᵷoʇ oʇ %s ssǝɹԀ", "gui.projecte.dark_matter_furnace.shortname": "ǝɔɐuɹnℲ Wᗡ", "gui.projecte.red_matter_furnace.shortname": "ǝɔɐuɹnℲ Wᴚ", "gui.projecte.relay.mk1": "IꞰW ʎɐꞁǝᴚ", "gui.projecte.relay.mk2": "IIꞰW ʎɐꞁǝᴚ", "gui.projecte.relay.mk3": "IIIꞰW ʎɐꞁǝᴚ", "item.projecte.aeternalis_fuel": "ꞁǝnℲ sᴉꞁɐuɹǝʇǝⱯ", "item.projecte.alchemical_coal": "ꞁɐoƆ ꞁɐɔᴉɯǝɥɔꞁⱯ", "item.projecte.arcana_ring": "ɐuɐɔɹⱯ ɟo ᵷuᴉᴚ", "item.projecte.archangel_smite": "ǝʇᴉɯS s,ꞁǝᵷuɐɥɔɹⱯ", "item.projecte.black_alchemical_bag": "(ʞɔɐꞁᗺ) ᵷɐᗺ ꞁɐɔᴉɯǝɥɔꞁⱯ", "item.projecte.black_hole_band": "puɐᗺ ǝꞁoH ʞɔɐꞁᗺ", "item.projecte.blue_alchemical_bag": "(ǝnꞁᗺ) ᵷɐᗺ ꞁɐɔᴉɯǝɥɔꞁⱯ", "item.projecte.body_stone": "ǝuoʇS ʎpoᗺ", "item.projecte.brown_alchemical_bag": "(uʍoɹᗺ) ᵷɐᗺ ꞁɐɔᴉɯǝɥɔꞁⱯ", "item.projecte.catalytic_lens": "suǝꞀ ɔᴉʇʎꞁɐʇɐƆ", "item.projecte.cyan_alchemical_bag": "(uɐʎƆ) ᵷɐᗺ ꞁɐɔᴉɯǝɥɔꞁⱯ", "item.projecte.dark_matter": "ɹǝʇʇɐW ʞɹɐᗡ", "item.projecte.destruction_catalyst": "ʇsʎꞁɐʇɐƆ uoᴉʇɔnɹʇsǝᗡ", "item.projecte.divining_rod_1": "(ʍoꞁ) poᴚ ᵷuᴉuᴉʌᴉᗡ", "item.projecte.divining_rod_2": "(ɯnᴉpǝɯ) poᴚ ᵷuᴉuᴉʌᴉᗡ", "item.projecte.divining_rod_3": "(ɥᵷᴉɥ) poᴚ ᵷuᴉuᴉʌᴉᗡ", "item.projecte.dm_axe": "ǝxⱯ ɹǝʇʇɐW ʞɹɐᗡ", "item.projecte.dm_boots": "sʇooᗺ ɹǝʇʇɐW ʞɹɐᗡ", "item.projecte.dm_chestplate": "ǝʇɐꞁdʇsǝɥƆ ɹǝʇʇɐW ʞɹɐᗡ", "item.projecte.dm_hammer": "ɹǝɯɯɐH ɹǝʇʇɐW ʞɹɐᗡ", "item.projecte.dm_helmet": "ʇǝɯꞁǝH ɹǝʇʇɐW ʞɹɐᗡ", "item.projecte.dm_hoe": "ǝoH ɹǝʇʇɐW ʞɹɐᗡ", "item.projecte.dm_leggings": "sᵷuᴉᵷᵷǝꞀ ɹǝʇʇɐW ʞɹɐᗡ", "item.projecte.dm_pick": "ǝxɐʞɔᴉԀ ɹǝʇʇɐW ʞɹɐᗡ", "item.projecte.dm_shears": "sɹɐǝɥS ɹǝʇʇɐW ʞɹɐᗡ", "item.projecte.dm_shovel": "ꞁǝʌoɥS ɹǝʇʇɐW ʞɹɐᗡ", "item.projecte.dm_sword": "pɹoʍS ɹǝʇʇɐW ʞɹɐᗡ", "item.projecte.evertide_amulet": "ʇǝꞁnɯⱯ ǝpᴉʇɹǝʌƎ", "item.projecte.gem_boots": "sʇooᗺ ɯǝ⅁", "item.projecte.gem_chestplate": "ǝʇɐꞁdʇsǝɥƆ ɯǝ⅁", "item.projecte.gem_helmet": "ʇǝɯꞁǝH ɯǝ⅁", "item.projecte.gem_leggings": "sᵷuᴉᵷᵷǝꞀ ɯǝ⅁", "item.projecte.gem_of_eternal_density": "ʎʇᴉsuǝᗡ ꞁɐuɹǝʇƎ ɟo ɯǝ⅁", "item.projecte.gray_alchemical_bag": "(ʎɐɹ⅁) ᵷɐᗺ ꞁɐɔᴉɯǝɥɔꞁⱯ", "item.projecte.green_alchemical_bag": "(uǝǝɹ⅁) ᵷɐᗺ ꞁɐɔᴉɯǝɥɔꞁⱯ", "item.projecte.harvest_goddess_band": "puɐᗺ ssǝppo⅁ ʇsǝʌɹɐH", "item.projecte.high_covalence_dust": "ʇsnᗡ ǝɔuǝꞁɐʌoƆ ɥᵷᴉH", "item.projecte.hyperkinetic_lens": "suǝꞀ ɔᴉʇǝuᴉʞɹǝdʎH", "item.projecte.ignition_ring": "ᵷuᴉᴚ uoᴉʇᴉuᵷI", "item.projecte.iron_band": "puɐᗺ uoɹI", "item.projecte.klein_star_drei": "ᴉǝɹᗡ ɹɐʇS uᴉǝꞁꞰ", "item.projecte.klein_star_ein": "uᴉƎ ɹɐʇS uᴉǝꞁꞰ", "item.projecte.klein_star_omega": "ɐᵷǝɯO ɹɐʇS uᴉǝꞁꞰ", "item.projecte.klein_star_sphere": "ǝɹǝɥdS ɹɐʇS uᴉǝꞁꞰ", "item.projecte.klein_star_vier": "ɹǝᴉΛ ɹɐʇS uᴉǝꞁꞰ", "item.projecte.klein_star_zwei": "ᴉǝʍZ ɹɐʇS uᴉǝꞁꞰ", "item.projecte.life_stone": "ǝuoʇS ǝɟᴉꞀ", "item.projecte.light_blue_alchemical_bag": "(ǝnꞁᗺ ʇɥᵷᴉꞀ) ᵷɐᗺ ꞁɐɔᴉɯǝɥɔꞁⱯ", "item.projecte.light_gray_alchemical_bag": "(ʎɐɹ⅁ ʇɥᵷᴉꞀ) ᵷɐᗺ ꞁɐɔᴉɯǝɥɔꞁⱯ", "item.projecte.lime_alchemical_bag": "(ǝɯᴉꞀ) ᵷɐᗺ ꞁɐɔᴉɯǝɥɔꞁⱯ", "item.projecte.low_covalence_dust": "ʇsnᗡ ǝɔuǝꞁɐʌoƆ ʍoꞀ", "item.projecte.magenta_alchemical_bag": "(ɐʇuǝᵷɐW) ᵷɐᗺ ꞁɐɔᴉɯǝɥɔꞁⱯ", "item.projecte.medium_covalence_dust": "ʇsnᗡ ǝɔuǝꞁɐʌoƆ ɯnᴉpǝW", "item.projecte.mercurial_eye": "ǝʎƎ ꞁɐᴉɹnɔɹǝW", "item.projecte.mind_stone": "ǝuoʇS puᴉW", "item.projecte.mobius_fuel": "ꞁǝnℲ snᴉqoW", "item.projecte.orange_alchemical_bag": "(ǝᵷuɐɹO) ᵷɐᗺ ꞁɐɔᴉɯǝɥɔꞁⱯ", "item.projecte.philosophers_stone": "ǝuoʇS s,ɹǝɥdosoꞁᴉɥԀ", "item.projecte.pink_alchemical_bag": "(ʞuᴉԀ) ᵷɐᗺ ꞁɐɔᴉɯǝɥɔꞁⱯ", "item.projecte.purple_alchemical_bag": "(ǝꞁdɹnԀ) ᵷɐᗺ ꞁɐɔᴉɯǝɥɔꞁⱯ", "item.projecte.red_alchemical_bag": "(pǝᴚ) ᵷɐᗺ ꞁɐɔᴉɯǝɥɔꞁⱯ", "item.projecte.red_matter": "ɹǝʇʇɐW pǝᴚ", "item.projecte.repair_talisman": "uɐɯsᴉꞁɐ⟘ ɹᴉɐdǝᴚ", "item.projecte.rm_axe": "ǝxⱯ ɹǝʇʇɐW pǝᴚ", "item.projecte.rm_boots": "sʇooᗺ ɹǝʇʇɐW pǝᴚ", "item.projecte.rm_chestplate": "ǝʇɐꞁdʇsǝɥƆ ɹǝʇʇɐW pǝᴚ", "item.projecte.rm_hammer": "ɹǝɯɯɐH ɹǝʇʇɐW pǝᴚ", "item.projecte.rm_helmet": "ʇǝɯꞁǝH ɹǝʇʇɐW pǝᴚ", "item.projecte.rm_hoe": "ǝoH ɹǝʇʇɐW pǝᴚ", "item.projecte.rm_katar": "ɹɐʇɐꞰ pǝᴚ", "item.projecte.rm_leggings": "sᵷuᴉᵷᵷǝꞀ ɹǝʇʇɐW pǝᴚ", "item.projecte.rm_morning_star": "ɹɐʇsᵷuᴉuɹoW pǝᴚ", "item.projecte.rm_pick": "ǝxɐʞɔᴉԀ ɹǝʇʇɐW pǝᴚ", "item.projecte.rm_shears": "sɹɐǝɥS ɹǝʇʇɐW pǝᴚ", "item.projecte.rm_shovel": "ꞁǝʌoɥS ɹǝʇʇɐW pǝᴚ", "item.projecte.rm_sword": "pɹoʍS ɹǝʇʇɐW pǝᴚ", "item.projecte.soul_stone": "ǝuoʇS ꞁnoS", "item.projecte.swiftwolf_rending_gale": "ǝꞁɐ⅁ ᵷuᴉpuǝᴚ s,ɟꞁoʍʇɟᴉʍS", "item.projecte.tome": "ǝᵷpǝꞁʍouꞰ ɟo ǝɯo⟘", "item.projecte.transmutation_tablet": "ʇǝꞁqɐ⟘ uoᴉʇɐʇnɯsuɐɹ⟘", "item.projecte.void_ring": "ᵷuᴉᴚ pᴉoΛ", "item.projecte.volcanite_amulet": "ʇǝꞁnɯⱯ ǝʇᴉuɐɔꞁoΛ", "item.projecte.watch_of_flowing_time": "ǝɯᴉ⟘ ᵷuᴉʍoꞁℲ ɟo ɥɔʇɐM", "item.projecte.white_alchemical_bag": "(ǝʇᴉɥM) ᵷɐᗺ ꞁɐɔᴉɯǝɥɔꞁⱯ", "item.projecte.yellow_alchemical_bag": "(ʍoꞁꞁǝ⅄) ᵷɐᗺ ꞁɐɔᴉɯǝɥɔꞁⱯ", "item.projecte.zero_ring": "ᵷuᴉᴚ oɹǝZ", "jei.projecte.collector": "sǝpɐɹᵷd∩ ꞁǝnℲ ɹoʇɔǝꞁꞁoƆ", "jei.projecte.world_transmute": "uoᴉʇɐʇnɯsuɐɹ⟘ pꞁɹoM", "jei.projecte.world_transmute.description": "ʇndʇno puoɔǝs ɹoɟ ʞɔᴉꞁɔ ʇɟᴉɥs 'pꞁɹoʍ uᴉ ʞɔᴉꞁƆ", "key.projecte.boots_toggle": "sʇɔǝɟɟƎ sʇooᗺ", "key.projecte.charge": "ǝᵷɹɐɥƆ", "key.projecte.extra_function": "uoᴉʇɔunℲ ɐɹʇxƎ", "key.projecte.fire_projectile": "ǝꞁᴉʇɔǝɾoɹԀ ǝɹᴉℲ", "key.projecte.helmet_toggle": "sʇɔǝɟɟƎ ʇǝɯꞁǝH", "key.projecte.mode": "ǝpoW ǝᵷuɐɥƆ", "misc.projecte.blacklist": "ʇsᴉꞁʞɔɐꞁᗺ", "misc.projecte.high_alchemist_joined": "ɹǝʌɹǝs ǝɥʇ pǝuᴉoɾ sɐɥ %s ʇsᴉɯǝɥɔꞁɐ ɥᵷᴉH", "misc.projecte.mod_name": "ƎʇɔǝɾoɹԀ", "misc.projecte.mode_switch": "%s :oʇ ʇǝᵷɹɐʇ ʇǝS", "misc.projecte.pack_description": "ƎʇɔǝɾoɹԀ ɹoɟ pǝsn sǝɔɹnosǝᴚ", "misc.projecte.seconds": "spuoɔǝs %s", "misc.projecte.seconds.every_tick": "(ʞɔᴉʇ ʎɹǝʌǝ) spuoɔǝs %s", "misc.projecte.update.available": "%s :uoᴉsɹǝΛ ¡ǝꞁqɐꞁᴉɐʌɐ ǝʇɐpdn ƎʇɔǝɾoɹԀ ʍǝN", "misc.projecte.update.get_it": "¡ǝɹǝɥ ʇᴉ ʇǝ⅁", "misc.projecte.whitelist": "ʇsᴉꞁǝʇᴉɥM", "mode.projecte.arcana.1": "oɹǝZ", "mode.projecte.arcana.2": "uoᴉʇᴉuᵷI", "mode.projecte.arcana.3": "ʇsǝʌɹɐH", "mode.projecte.arcana.4": "⅁ᴚMS", "mode.projecte.current": "%s :ǝpoW", "mode.projecte.invalid": "ǝpoW pᴉꞁɐʌuI", "mode.projecte.katar.1": "ǝꞁᴉʇsoH ʎɐꞁS", "mode.projecte.katar.2": "ꞁꞁⱯ ʎɐꞁS", "mode.projecte.mercurial_eye.1": "uoᴉʇɐǝɹƆ", "mode.projecte.mercurial_eye.2": "uoᴉsuǝʇxƎ", "mode.projecte.mercurial_eye.3": "ɔᴉssɐꞁƆ-uoᴉsuǝʇxƎ", "mode.projecte.mercurial_eye.4": "uoᴉʇɐʇnɯsuɐɹ⟘", "mode.projecte.mercurial_eye.5": "ɔᴉssɐꞁƆ-uoᴉʇɐʇnɯsuɐɹ⟘", "mode.projecte.mercurial_eye.6": "ɹɐꞁꞁᴉԀ", "mode.projecte.morning_star.1": "pɹɐpuɐʇS", "mode.projecte.morning_star.2": "ʇoɥsꞁꞁɐ⟘ xƐ", "mode.projecte.morning_star.3": "ʇoɥsǝpᴉM xƐ", "mode.projecte.morning_star.4": "ʇoɥsᵷuoꞀ xƐ", "mode.projecte.philosopher.1": "ǝqnƆ", "mode.projecte.philosopher.2": "ꞁǝuɐԀ", "mode.projecte.philosopher.3": "ǝuᴉꞀ", "mode.projecte.pick.1": "pɹɐpuɐʇS", "mode.projecte.pick.2": "ʇoɥsꞁꞁɐ⟘ xƐ", "mode.projecte.pick.3": "ʇoɥsǝpᴉM xƐ", "mode.projecte.pick.4": "ʇoɥsᵷuoꞀ xƐ", "mode.projecte.red_sword.1": "ǝꞁᴉʇsoH ʎɐꞁS", "mode.projecte.red_sword.2": "ꞁꞁⱯ ʎɐꞁS", "mode.projecte.switch": "ǝpoW %s oʇ pǝɥɔʇᴉʍS", "pedestal.projecte.archangel.1": "sqoɯ ʎqɹɐǝu ʇɐ sʍoɹɹɐ sǝɹᴉℲ", "pedestal.projecte.archangel.2": "%s ʎɹǝʌǝ sɹǝᵷᵷᴉɹ⟘", "pedestal.projecte.black_hole_band.1": "sdoɹp ɯǝʇᴉ ʎqɹɐǝu uᴉ sʞɔnS", "pedestal.projecte.black_hole_band.2": "sǝᴉɹoʇuǝʌuᴉ ʇuǝɔɐɾpɐ uᴉ sdɯnᗡ", "pedestal.projecte.body_stone.1": "ɹǝᵷunɥ ,sɹǝʎɐꞁd ʎqɹɐǝu sǝɹoʇsǝᴚ", "pedestal.projecte.body_stone.2": "%s ʎɹǝʌǝ ʞuɐɥs ɐ ɟꞁɐH", "pedestal.projecte.evertide.1": "sɯɹoʇs ʍous/uᴉɐɹ ǝʇɐǝɹƆ", "pedestal.projecte.evertide.2": "%s ʎɹǝʌǝ uᴉɐɹ ʇɹɐʇs oʇ sʇdɯǝʇʇⱯ", "pedestal.projecte.harvest_goddess.1": "sdoɹɔ ʎqɹɐǝu ɟo ɥʇʍoɹᵷ sǝʇɐɹǝꞁǝɔɔⱯ", "pedestal.projecte.harvest_goddess.2": "sdoɹɔ uʍoɹᵷ ʎqɹɐǝu sʇsǝʌɹɐH", "pedestal.projecte.harvest_goddess.3": "%s ʎɹǝʌǝ sǝʇɐʌᴉʇɔⱯ", "pedestal.projecte.ignition.1": "ʇsnqɯoɔ sqoɯ ʎqɹɐǝN", "pedestal.projecte.ignition.2": "%s ʎɹǝʌǝ sǝʇɐʌᴉʇɔⱯ", "pedestal.projecte.item_disabled": "¡pǝꞁqɐsᴉp uǝǝq sɐɥ uoᴉʇɔunɟ ꞁɐʇsǝpǝԀ", "pedestal.projecte.life_stone.1": "sʇɹɐǝɥ puɐ ɹǝᵷunɥ ɥʇoq sǝɹoʇsǝᴚ", "pedestal.projecte.life_stone.2": "%s ʎɹǝʌǝ ʞuɐɥs puɐ ʇɹɐǝɥ ɐ ɟꞁɐH", "pedestal.projecte.mind_stone": "ǝuoʇS puᴉW ǝɥʇ oʇuᴉ sqɹo ԀX ʎqɹɐǝu sʞɔnS", "pedestal.projecte.on_pedestal": ":ꞁɐʇsǝpǝԀ uO", "pedestal.projecte.repair_talisman.1": "sɯǝʇᴉ ,sɹǝʎɐꞁd ʎqɹɐǝu sɹᴉɐdǝᴚ", "pedestal.projecte.repair_talisman.2": "%s ʎɹǝʌǝ ʎʇᴉꞁᴉqɐɹnp ⥝ sǝɹoʇsǝᴚ", "pedestal.projecte.soul_stone.1": "sɹǝʎɐꞁd ʎqɹɐǝu sꞁɐǝH", "pedestal.projecte.soul_stone.2": "%s ʎɹǝʌǝ ʇɹɐǝɥ ɐ ɟꞁɐH", "pedestal.projecte.swrg.1": "sqoɯ ʎqɹɐǝu ʇɐ ᵷuᴉuʇɥᵷᴉꞁ sʇooɥS", "pedestal.projecte.swrg.2": "%s ʎɹǝʌǝ sǝʇɐʌᴉʇɔⱯ", "pedestal.projecte.time_watch.1": "ʞɔᴉʇ ʎɹǝʌǝ sʞɔoꞁq ʎqɹɐǝu oʇ sʞɔᴉʇ snuoq %s sǝʌᴉ⅁", "pedestal.projecte.time_watch.2": "pǝǝds ǝɥʇ sǝɯᴉʇ %s ǝʌoɯ sqoɯ ʎqɹɐǝu 'ʞɔᴉʇ ɥɔɐƎ", "pedestal.projecte.tooltip.1": "˙ǝʌoɯǝɹ oʇ ʞɔᴉꞁɔ ʇɟǝꞁ 'ɯǝʇᴉ uɐ ʇɹǝsuᴉ oʇ ʞɔᴉꞁɔ ʇɥᵷᴉᴚ", "pedestal.projecte.tooltip.2": "¡ǝʇɐʌᴉʇɔɐ oʇ puɐɥ ʎʇdɯǝ ɥʇᴉʍ ʞɔᴉꞁɔ ʇɥᵷᴉᴚ", "pedestal.projecte.volcanite.1": "sɯɹoʇs ʍous/uᴉɐɹ sʇuǝʌǝɹԀ", "pedestal.projecte.volcanite.2": "%s ʎɹǝʌǝ ɹǝɥʇɐǝʍ doʇs oʇ sʇdɯǝʇʇⱯ", "pedestal.projecte.zero.1": "sǝᴉʇᴉʇuǝ sǝɥsᴉnᵷuᴉʇxƎ", "pedestal.projecte.zero.2": "sᵷuᴉpunoɹɹns sǝzǝǝɹℲ", "pedestal.projecte.zero.3": "%s ʎɹǝʌǝ sǝʇɐʌᴉʇɔⱯ", "sound_event.projecte.charge": "pǝᵷɹɐɥƆ ǝɔᴉʌǝᗡ", "sound_event.projecte.destruct": "uoᴉʇɔnɹʇsǝᗡ", "sound_event.projecte.heal": "pǝɯɹoɟɹǝԀ ᵷuᴉꞁɐǝH", "sound_event.projecte.power": "pǝɹǝʍoԀ ǝɔᴉʌǝᗡ", "sound_event.projecte.transmute": "pǝʇnɯsuɐɹ⟘ ʞɔoꞁᗺ", "sound_event.projecte.uncharge": "pǝᵷɹɐɥɔu∩ ǝɔᴉʌǝᗡ", "sound_event.projecte.watermagic": "ɔᴉᵷɐW ɹǝʇɐM", "sound_event.projecte.windmagic": "ɔᴉᵷɐW puᴉM", "time_watch.projecte.disabled": "uᴉɯpɐ ɹǝʌɹǝs ʎq pǝꞁqɐsᴉp ɯǝʇI", "time_watch.projecte.fast_forward": "pɹɐʍɹoℲ-ʇsɐℲ", "time_watch.projecte.mode": "%s :ǝpoɯ ꞁoɹʇuoɔ ǝɯᴉ⟘", "time_watch.projecte.mode_switch": "%s :oʇ ʇǝs ǝpoɯ ꞁoɹʇuoɔ ǝɯᴉ⟘", "time_watch.projecte.off": "ɟɟO", "time_watch.projecte.rewind": "puᴉʍǝᴚ", "tooltip.projecte.arcana.inactive": "¡ǝʌᴉʇɔɐuI", "tooltip.projecte.evertide.1": "ǝꞁᴉʇɔǝɾoɹd ɹǝʇɐʍ ɐ ǝɹᴉɟ oʇ %s ssǝɹԀ", "tooltip.projecte.evertide.2": "ʇǝʞɔnq ɹǝʇɐʍ ǝʇᴉuᴉɟuᴉ uɐ sɐ sʇɔⱯ", "tooltip.projecte.evertide.3": "suoɹpꞁnɐɔ puɐ sʞuɐʇ ꞁꞁᴉɟ oʇ ʞɔᴉꞁɔ ʇɥᵷᴉᴚ", "tooltip.projecte.evertide.4": "¡ǝǝɹɟ ʎꞁǝʇǝꞁdɯoɔ ǝɹɐ suoᴉʇɐɹǝdo ꞁꞁⱯ", "tooltip.projecte.gem_density.1": "oᵷ ǝɥʇ uo sɯǝʇᴉ sǝsuǝpuoƆ", "tooltip.projecte.gem_density.2": "%s :ʇǝᵷɹɐʇ ʇuǝɹɹnƆ", "tooltip.projecte.gem_density.3": "ʇǝᵷɹɐʇ ǝᵷuɐɥɔ oʇ %s ssǝɹԀ", "tooltip.projecte.gem_density.4": "ʇsᴉꞁǝʇᴉɥʍ/ʇsᴉꞁʞɔɐꞁq dn ʇǝs oʇ ʞɔᴉꞁɔ ʇɥᵷᴉᴚ", "tooltip.projecte.gem_density.5": "ǝꞁᵷᵷoʇ oʇ ʞɔᴉꞁɔ ʇɥᵷᴉɹ ʇɟᴉɥS", "tooltip.projecte.philostone": "pᴉɹᵷ ᵷuᴉʇɟɐɹɔ ɐ uǝdo oʇ %s ssǝɹԀ", "tooltip.projecte.stored_xp": "%s :ԀX pǝɹoʇS", "tooltip.projecte.time_watch.1": "ǝɯᴉʇ ɟo ɹǝʇsɐɯ ǝɥʇ ǝɯoɔǝᗺ", "tooltip.projecte.time_watch.2": "ǝpoɯ ǝᵷuɐɥɔ oʇ ʞɔᴉꞁɔ ʇɥᵷᴉᴚ", "tooltip.projecte.tome": "pǝuɹɐǝꞁ uǝɥʍ ǝᵷpǝꞁʍouʞ uoᴉʇɐʇnɯsuɐɹʇ ꞁꞁɐ sʞɔoꞁu∩", "tooltip.projecte.volcanite.1": "ǝꞁᴉʇɔǝɾoɹd ɐʌɐꞁ ɐ ǝɹᴉɟ oʇ %s ssǝɹԀ", "tooltip.projecte.volcanite.2": "ʇǝʞɔnq ɐʌɐꞁ ꞁꞁnɟ ʎꞁǝʇᴉuᴉɟuᴉ sɐ sʇɔⱯ", "tooltip.projecte.volcanite.3": "suoɹpꞁnɐɔ puɐ sʞuɐʇ ꞁꞁᴉɟ oʇ ʞɔᴉꞁɔ ʇɥᵷᴉᴚ", "tooltip.projecte.volcanite.4": "¡ƆWƎ ᘔƐ ʇsoɔ suoᴉʇɐɹǝdo ꞁꞁⱯ", "transmutation.projecte.learned.1": "Ꞁ", "transmutation.projecte.learned.2": "ǝ", "transmutation.projecte.learned.3": "ɐ", "transmutation.projecte.learned.4": "ɹ", "transmutation.projecte.learned.5": "u", "transmutation.projecte.learned.6": "ǝ", "transmutation.projecte.learned.7": "p", "transmutation.projecte.learned.8": "¡", "transmutation.projecte.transmute": "uoᴉʇɐʇnɯsuɐɹ⟘", "transmutation.projecte.unlearned.1": "∩", "transmutation.projecte.unlearned.2": "u", "transmutation.projecte.unlearned.3": "ꞁ", "transmutation.projecte.unlearned.4": "ǝ", "transmutation.projecte.unlearned.5": "ɐ", "transmutation.projecte.unlearned.6": "ɹ", "transmutation.projecte.unlearned.7": "u", "transmutation.projecte.unlearned.8": "ǝ", "transmutation.projecte.unlearned.9": "p"}