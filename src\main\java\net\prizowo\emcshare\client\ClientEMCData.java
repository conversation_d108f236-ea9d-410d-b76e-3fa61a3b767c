package net.prizowo.emcshare.client;

import java.math.BigInteger;

/**
 * Client-side storage for shared EMC data
 */
public class ClientEMCData {
    private static boolean usingSharedEMC = false;
    private static BigInteger sharedEMC = BigInteger.ZERO;
    private static String teamName = "";
    private static int memberCount = 0;
    private static long lastUpdateTime = 0;

    public static void updateSharedEMC(boolean useShared, BigInteger emc, String team, int members) {
        // Validate input data
        if (emc == null) {
            emc = BigInteger.ZERO;
        }
        if (team == null) {
            team = "";
        }
        if (members < 0) {
            members = 0;
        }

        usingSharedEMC = useShared;
        sharedEMC = emc;
        teamName = team;
        memberCount = members;
        lastUpdateTime = System.currentTimeMillis();
    }

    public static boolean isUsingSharedEMC() {
        return usingSharedEMC;
    }

    public static BigInteger getSharedEMC() {
        return sharedEMC;
    }

    public static String getTeamName() {
        return teamName;
    }

    public static int getMemberCount() {
        return memberCount;
    }

    public static long getLastUpdateTime() {
        return lastUpdateTime;
    }

    public static boolean isDataFresh() {
        return System.currentTimeMillis() - lastUpdateTime < 5000; // 5 seconds
    }
}
