{"advancements.projecte.alch_bag": "Pocket storage!", "advancements.projecte.alch_bag.description": "All the wonders of an alchemical chest, in your pocket.", "advancements.projecte.alch_chest": "Storage Upgrade!", "advancements.projecte.alch_chest.description": "A \"little\" chest upgrade.", "advancements.projecte.collector": "The power of the sun!", "advancements.projecte.collector.description": "Now the fun begins.", "advancements.projecte.condenser": "Condense the world!", "advancements.projecte.condenser.description": "MORE DIAMONDS!", "advancements.projecte.dark_matter": "All that Matters.", "advancements.projecte.dark_matter.description": "It looks... weird....", "advancements.projecte.dark_matter_block": "A block that Matters!", "advancements.projecte.dark_matter_block.description": "Stuffing matter together. Because that's a good idea.", "advancements.projecte.dark_matter_furnace": "Hot matter!", "advancements.projecte.dark_matter_furnace.description": "A furnace is even better when made from dark matter.", "advancements.projecte.dark_matter_pickaxe": "Using Matter on Matter", "advancements.projecte.dark_matter_pickaxe.description": "Because why not?", "advancements.projecte.description": "Correspondent Commerce?", "advancements.projecte.klein_star": "EMC Batteries", "advancements.projecte.klein_star.description": "Storing EMC for a rainy day.", "advancements.projecte.klein_star_big": "BIG EMC Batteries", "advancements.projecte.klein_star_big.description": "Holding the universe in your pocket.", "advancements.projecte.philo_stone": "An alchemist's best friend!", "advancements.projecte.philo_stone.description": "Let's get things started! Craft a philosopher's stone", "advancements.projecte.red_matter": "Even better Matter!", "advancements.projecte.red_matter.description": "The space time continuum may be broken.", "advancements.projecte.red_matter_block": "Red and shiny!", "advancements.projecte.red_matter_block.description": "Now you're getting somewhere!", "advancements.projecte.red_matter_furnace": "Even hotter matter!", "advancements.projecte.red_matter_furnace.description": "Wow, that thing is fast.", "advancements.projecte.red_matter_pickaxe": "Is this thing safe?", "advancements.projecte.red_matter_pickaxe.description": "Probably not.", "advancements.projecte.relay": "Power flowers!", "advancements.projecte.relay.description": "Linking collectors together for even more power.", "advancements.projecte.transmutation_table": "Transmute this into that!", "advancements.projecte.transmutation_table.description": "The beginning (and end) of everything.", "advancements.projecte.transmutation_tablet": "Transmutation on the go!", "advancements.projecte.transmutation_tablet.description": "And then you thought things couldn't get better.", "block.projecte.aeternalis_fuel_block": "Aeternalis Fuel Block", "block.projecte.alchemical_chest": "Alchemical Chest", "block.projecte.alchemical_coal_block": "Alchemical Coal Block", "block.projecte.collector_mk1": "Energy Collector MK1", "block.projecte.collector_mk2": "Energy Collector MK2", "block.projecte.collector_mk3": "Energy Collector MK3", "block.projecte.condenser_mk1": "Energy Condenser", "block.projecte.condenser_mk2": "Energy Condenser MK2", "block.projecte.dark_matter_block": "Dark Matter Block", "block.projecte.dm_furnace": "Dark Matter Furnace", "block.projecte.dm_pedestal": "Dark Matter Pedestal", "block.projecte.interdiction_torch": "Interdiction Torch", "block.projecte.mobius_fuel_block": "Mobius Fuel Block", "block.projecte.nova_cataclysm": "Nova Cataclysm", "block.projecte.nova_catalyst": "Nova Catalyst", "block.projecte.red_matter_block": "Red Matter Block", "block.projecte.relay_mk1": "Anti-Matter Relay MK1", "block.projecte.relay_mk2": "Anti-Matter Relay MK2", "block.projecte.relay_mk3": "Anti-Matter Relay MK3", "block.projecte.rm_furnace": "Red Matter Furnace", "block.projecte.transmutation_table": "Transmutation Table", "command.projecte.clear_knowledge.notify": "Your transmutation knowledge was cleared by %s!", "command.projecte.clear_knowledge.success": "Cleared knowledge for: %s", "command.projecte.dump_missing_emc.multiple_missing": "%s Items are missing an EMC value, printing to client log.", "command.projecte.dump_missing_emc.none_missing": "All Items have an EMC value.", "command.projecte.dump_missing_emc.one_missing": "One Item is missing an EMC value, printing to server log.", "command.projecte.emc.add.success": "Added %s EMC to %s.", "command.projecte.emc.get.success": "%s has %s EMC.", "command.projecte.emc.invalid": "The value \"%s\" is invalid, it must be a positive integer.", "command.projecte.emc.invalid_item": "Error: The item or tag \"%s\" was not found!", "command.projecte.emc.negative": "Cannot remove %s EMC from %s as this would make their EMC negative.", "command.projecte.emc.no_item": "Please give an item or tag to change", "command.projecte.emc.remove.success": "Removed %s EMC from %s.", "command.projecte.emc.set.success": "Set the EMC of %s to %s.", "command.projecte.emc.test.fail": "%s does not have enough EMC to remove %s.", "command.projecte.emc.test.success": "%s does have enough EMC to remove %s.", "command.projecte.knowledge.clear.fail": "%s does not have any knowledge to clear.", "command.projecte.knowledge.clear.success": "Successfully cleared the knowledge of %s.", "command.projecte.knowledge.invalid": "The item \"%s\" does not  have an EMC value, and cannot be learned.", "command.projecte.knowledge.learn.fail": "%s already has knowledge of %s.", "command.projecte.knowledge.learn.success": "%s has successfully learned %s.", "command.projecte.knowledge.test.fail": "%s does not have knowledge of %s.", "command.projecte.knowledge.test.success": "%s has knowledge of %s.", "command.projecte.knowledge.unlearn.fail": "%s does not have knowledge of %s.", "command.projecte.knowledge.unlearn.success": "%s has successfully unlearned %s.", "command.projecte.provider.fail": "Failed to get provider for %s.", "command.projecte.reload.notice": "Restart or use \"/reload\" when all changes are complete.", "command.projecte.remove.success": "Removed EMC value for %s.", "command.projecte.reset.success": "Reset EMC value for %s.", "command.projecte.set.success": "Set EMC value for %s to %s!", "command.projecte.showbag.named": "%s (%s)", "command.projecte.showbag.offline.notfound": "UUID %s not found in playerdata/", "config.jade.plugin_projecte.emc_provider": "EMC Provider", "curios.identifier.klein_star": "Klein Star", "divining_rod.projecte.avg_emc": "Average EMC for %s blocks: %s", "divining_rod.projecte.max_emc": "Max EMC: %s", "divining_rod.projecte.range.16": "16x3x3", "divining_rod.projecte.range.3": "3x3x3", "divining_rod.projecte.range.64": "64x3x3", "divining_rod.projecte.second_max": "Second Max EMC: %s", "divining_rod.projecte.third_max": "Third Max EMC: %s", "emc.projecte.emc": "%s EMC", "emc.projecte.has_knowledge": "Learned", "emc.projecte.max_gen_rate": "Maximum Generation Rate: %s EMC/s", "emc.projecte.max_output_rate": "Maximum Output Rate: %s EMC/s", "emc.projecte.max_storage": "Maximum Storage: %s EMC", "emc.projecte.no_knowledge": "Unlearned", "emc.projecte.postfix.0": "%s Trillion", "emc.projecte.postfix.1": "%s Quadrillion", "emc.projecte.postfix.10": "%s Tredecillion", "emc.projecte.postfix.11": "%s <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "emc.projecte.postfix.12": "%s Quin<PERSON>cillion", "emc.projecte.postfix.13": "%s Sexdecillion", "emc.projecte.postfix.14": "%s Septendecillion", "emc.projecte.postfix.15": "%s Octodecillion", "emc.projecte.postfix.16": "%s Novemdecillion", "emc.projecte.postfix.17": "%s Vigintillion", "emc.projecte.postfix.2": "%s Quintillion", "emc.projecte.postfix.3": "%s Sextillion", "emc.projecte.postfix.4": "%s Septillion", "emc.projecte.postfix.5": "%s Octillion", "emc.projecte.postfix.6": "%s Nonillion", "emc.projecte.postfix.7": "%s Decillion", "emc.projecte.postfix.8": "%s Undecillion", "emc.projecte.postfix.9": "%s Duodecillion", "emc.projecte.stored": "Stored EMC: %s", "emc.projecte.too_much": "WAY TOO MUCH", "emc.projecte.tooltip": "EMC: %s", "emc.projecte.tooltip.stack": "Stack EMC: %s", "emc.projecte.tooltip.stack.with_sell": "Stack EMC: %s (%s)", "emc.projecte.tooltip.with_sell": "EMC: %s (%s)", "entity.projecte.fire_projectile": "Fireball", "entity.projecte.homing_arrow": "Homing Arrow", "entity.projecte.lava_projectile": "<PERSON><PERSON>", "entity.projecte.lens_projectile": "Explosive Lens", "entity.projecte.mob_randomizer": "Randomizer Orb", "entity.projecte.nova_cataclysm_primed": "Primed Nova Cataclysm", "entity.projecte.nova_catalyst_primed": "Primed Nova Catalyst", "entity.projecte.swrg_projectile": "SWRG Projectile", "entity.projecte.water_projectile": "Water Orb", "gem.projecte.activate": "Activated Gem Armor Offensive Abilities", "gem.projecte.deactivate": "Deactivated Gem Armor Offensive Abilities", "gem.projecte.disabled": "DISABLED", "gem.projecte.enabled": "ENABLED", "gem.projecte.lore.chest": "Infernal Armor", "gem.projecte.lore.feet": "Hurricane Boots", "gem.projecte.lore.helm": "<PERSON><PERSON> He<PERSON>et", "gem.projecte.lore.legs": "<PERSON><PERSON><PERSON>", "gem.projecte.night_vision": "Night Vision: %s", "gem.projecte.night_vision.prompt": "Press %s to toggle Night Vision", "gem.projecte.step_assist": "Step Assist: %s", "gem.projecte.step_assist.prompt": "Press %s to toggle Step Assist", "gui.projecte.dark_matter_furnace.shortname": "DM Furnace", "gui.projecte.red_matter_furnace.shortname": "RM Furnace", "gui.projecte.relay.mk1": "Relay MKI", "gui.projecte.relay.mk2": "Relay MKII", "gui.projecte.relay.mk3": "Relay MKIII", "item.projecte.aeternalis_fuel": "Aeternalis Fuel", "item.projecte.alchemical_coal": "Alchemical Coal", "item.projecte.arcana_ring": "Ring of Arcana", "item.projecte.archangel_smite": "<PERSON><PERSON><PERSON>'s Smite", "item.projecte.black_alchemical_bag": "Alchemical Bag (Black)", "item.projecte.black_hole_band": "Black Hole Band", "item.projecte.blue_alchemical_bag": "Alchemical Bag (Blue)", "item.projecte.body_stone": "Body Stone", "item.projecte.brown_alchemical_bag": "Alchemical Bag (Brown)", "item.projecte.catalytic_lens": "Catalytic Lens", "item.projecte.cyan_alchemical_bag": "Alchemical Bag (Cyan)", "item.projecte.dark_matter": "Dark Matter", "item.projecte.destruction_catalyst": "Destruction Catalyst", "item.projecte.divining_rod_1": "Divining Rod (low)", "item.projecte.divining_rod_2": "Di<PERSON><PERSON> (medium)", "item.projecte.divining_rod_3": "Divining Rod (high)", "item.projecte.dm_axe": "Dark Matter Axe", "item.projecte.dm_boots": "Dark Matter Boots", "item.projecte.dm_chestplate": "Dark Matter Chestplate", "item.projecte.dm_hammer": "Dark Matter Hammer", "item.projecte.dm_helmet": "Dark Matter Helmet", "item.projecte.dm_hoe": "Dark Matter Hoe", "item.projecte.dm_leggings": "Dark Matter Leggings", "item.projecte.dm_pick": "Dark Matter Pickaxe", "item.projecte.dm_shears": "Dark Matter Shears", "item.projecte.dm_shovel": "Dark <PERSON>el", "item.projecte.dm_sword": "Dark Matter Sword", "item.projecte.evertide_amulet": "Evertide Amulet", "item.projecte.gem_boots": "Gem Boots", "item.projecte.gem_chestplate": "Gem Chestplate", "item.projecte.gem_helmet": "<PERSON><PERSON>", "item.projecte.gem_leggings": "<PERSON><PERSON>", "item.projecte.gem_of_eternal_density": "Gem of Eternal Density", "item.projecte.gray_alchemical_bag": "Alchemical Bag (Gray)", "item.projecte.green_alchemical_bag": "Alchemical Bag (Green)", "item.projecte.harvest_goddess_band": "Harvest Goddess Band", "item.projecte.high_covalence_dust": "High Covalence Dust", "item.projecte.hyperkinetic_lens": "Hyperkinetic Lens", "item.projecte.ignition_ring": "Ignition Ring", "item.projecte.iron_band": "Iron Band", "item.projecte.klein_star_drei": "Klein Star Drei", "item.projecte.klein_star_ein": "Klein Star Ein", "item.projecte.klein_star_omega": "Klein Star Omega", "item.projecte.klein_star_sphere": "Klein Star Sphere", "item.projecte.klein_star_vier": "Klein Star Vier", "item.projecte.klein_star_zwei": "Klein Star Zwei", "item.projecte.life_stone": "Life Stone", "item.projecte.light_blue_alchemical_bag": "Alchemical Bag (Light Blue)", "item.projecte.light_gray_alchemical_bag": "Alchemical Bag (Light Gray)", "item.projecte.lime_alchemical_bag": "Alchemical Bag (Lime)", "item.projecte.low_covalence_dust": "Low Covalence Dust", "item.projecte.magenta_alchemical_bag": "Alchemical Bag (Magenta)", "item.projecte.medium_covalence_dust": "Medium Covalence Dust", "item.projecte.mercurial_eye": "Mercurial Eye", "item.projecte.mind_stone": "Mind Stone", "item.projecte.mobius_fuel": "Mobius Fuel", "item.projecte.orange_alchemical_bag": "Alchemical Bag (Orange)", "item.projecte.philosophers_stone": "Philoso<PERSON>'s Stone", "item.projecte.pink_alchemical_bag": "Alchemical Bag (Pink)", "item.projecte.purple_alchemical_bag": "Alchemical Bag (Purple)", "item.projecte.red_alchemical_bag": "Alchemical Bag (Red)", "item.projecte.red_matter": "Red Matter", "item.projecte.repair_talisman": "<PERSON><PERSON>", "item.projecte.rm_axe": "Red Matter Axe", "item.projecte.rm_boots": "Red Matter Boots", "item.projecte.rm_chestplate": "Red Matter Chestplate", "item.projecte.rm_hammer": "Red Matter Hammer", "item.projecte.rm_helmet": "Red Matter Helmet", "item.projecte.rm_hoe": "Red Matter Hoe", "item.projecte.rm_katar": "Red Katar", "item.projecte.rm_leggings": "Red Matter Leggings", "item.projecte.rm_morning_star": "Red Morningstar", "item.projecte.rm_pick": "Red Matter Pickaxe", "item.projecte.rm_shears": "Red Matter Shears", "item.projecte.rm_shovel": "Red Matter Shovel", "item.projecte.rm_sword": "Red Matter Sword", "item.projecte.soul_stone": "Soul Stone", "item.projecte.swiftwolf_rending_gale": "Swiftwolf's Rending Gale", "item.projecte.tome": "<PERSON><PERSON> of Knowledge", "item.projecte.transmutation_tablet": "Transmutation Tablet", "item.projecte.void_ring": "Void Ring", "item.projecte.volcanite_amulet": "Volcanite Amulet", "item.projecte.watch_of_flowing_time": "Watch of Flowing Time", "item.projecte.white_alchemical_bag": "Alchemical Bag (White)", "item.projecte.yellow_alchemical_bag": "Alchemical Bag (Yellow)", "item.projecte.zero_ring": "Zero Ring", "jei.projecte.collector": "Collector Fuel Upgrades", "jei.projecte.world_transmute": "World Transmutation", "jei.projecte.world_transmute.description": "Click in world, shift click for second output", "key.projecte.boots_toggle": "Boots Effects", "key.projecte.charge": "Charge", "key.projecte.extra_function": "Extra Function", "key.projecte.fire_projectile": "Fire Projectile", "key.projecte.helmet_toggle": "Helmet Effects", "key.projecte.mode": "Change Mode", "misc.projecte.blacklist": "Blacklist", "misc.projecte.high_alchemist_joined": "High alchemist %s has joined the server", "misc.projecte.mod_name": "ProjectE", "misc.projecte.mode_switch": "Set target to: %s", "misc.projecte.pack_description": "Resources used for ProjectE", "misc.projecte.seconds": "%s seconds", "misc.projecte.seconds.every_tick": "%s seconds (every tick)", "misc.projecte.update.available": "New ProjectE update available! Version: %s", "misc.projecte.update.get_it": "Get it here!", "misc.projecte.whitelist": "Whitelist", "mode.projecte.arcana.1": "Zero", "mode.projecte.arcana.2": "Ignition", "mode.projecte.arcana.3": "Harvest", "mode.projecte.arcana.4": "SWRG", "mode.projecte.current": "Mode: %s", "mode.projecte.invalid": "Invalid Mode", "mode.projecte.katar.1": "Slay Hostile", "mode.projecte.katar.2": "Slay All", "mode.projecte.mercurial_eye.1": "Creation", "mode.projecte.mercurial_eye.2": "Extension", "mode.projecte.mercurial_eye.3": "Extension-Classic", "mode.projecte.mercurial_eye.4": "Transmutation", "mode.projecte.mercurial_eye.5": "Transmutation-Classic", "mode.projecte.mercurial_eye.6": "<PERSON><PERSON>", "mode.projecte.morning_star.1": "Standard", "mode.projecte.morning_star.2": "3x Tallshot", "mode.projecte.morning_star.3": "3x Wideshot", "mode.projecte.morning_star.4": "3x Longshot", "mode.projecte.philosopher.1": "C<PERSON>", "mode.projecte.philosopher.2": "Panel", "mode.projecte.philosopher.3": "Line", "mode.projecte.pick.1": "Standard", "mode.projecte.pick.2": "3x Tallshot", "mode.projecte.pick.3": "3x Wideshot", "mode.projecte.pick.4": "3x Longshot", "mode.projecte.red_sword.1": "Slay Hostile", "mode.projecte.red_sword.2": "Slay All", "mode.projecte.switch": "Switched to %s Mode", "pedestal.projecte.archangel.1": "Fires arrows at nearby mobs", "pedestal.projecte.archangel.2": "Triggers every %s", "pedestal.projecte.black_hole_band.1": "Sucks in nearby item drops", "pedestal.projecte.black_hole_band.2": "Dumps in adjacent inventories", "pedestal.projecte.body_stone.1": "Restores nearby players' hunger", "pedestal.projecte.body_stone.2": "Half a shank every %s", "pedestal.projecte.evertide.1": "Create rain/snow storms", "pedestal.projecte.evertide.2": "Attempts to start rain every %s", "pedestal.projecte.harvest_goddess.1": "Accelerates growth of nearby crops", "pedestal.projecte.harvest_goddess.2": "Harvests nearby grown crops", "pedestal.projecte.harvest_goddess.3": "Activates every %s", "pedestal.projecte.ignition.1": "Nearby mobs combust", "pedestal.projecte.ignition.2": "Activates every %s", "pedestal.projecte.item_disabled": "Pedestal function has been disabled!", "pedestal.projecte.life_stone.1": "Restores both hunger and hearts", "pedestal.projecte.life_stone.2": "Half a heart and shank every %s", "pedestal.projecte.mind_stone": "Sucks nearby XP orbs into the Mind Stone", "pedestal.projecte.on_pedestal": "On Pedestal:", "pedestal.projecte.repair_talisman.1": "Repairs nearby players' items", "pedestal.projecte.repair_talisman.2": "Restores 1 durability every %s", "pedestal.projecte.soul_stone.1": "Heals nearby players", "pedestal.projecte.soul_stone.2": "Half a heart every %s", "pedestal.projecte.swrg.1": "Shoots lightning at nearby mobs", "pedestal.projecte.swrg.2": "Activates every %s", "pedestal.projecte.time_watch.1": "Gives %s bonus ticks to nearby blocks every tick", "pedestal.projecte.time_watch.2": "Each tick, nearby mobs move %s times the speed", "pedestal.projecte.tooltip.1": "Right click to insert an item, left click to remove.", "pedestal.projecte.tooltip.2": "Right click with empty hand to activate!", "pedestal.projecte.volcanite.1": "Prevents rain/snow storms", "pedestal.projecte.volcanite.2": "Attempts to stop weather every %s", "pedestal.projecte.zero.1": "Extinguishes entities", "pedestal.projecte.zero.2": "Freezes surroundings", "pedestal.projecte.zero.3": "Activates every %s", "sound_event.projecte.charge": "Device Charged", "sound_event.projecte.destruct": "Destruction", "sound_event.projecte.heal": "Healing Performed", "sound_event.projecte.power": "<PERSON>ce Powered", "sound_event.projecte.transmute": "Block Transmuted", "sound_event.projecte.uncharge": "Device Uncharged", "sound_event.projecte.watermagic": "Water Magic", "sound_event.projecte.windmagic": "Wind Magic", "time_watch.projecte.disabled": "Item disabled by server admin", "time_watch.projecte.fast_forward": "Fast-Forward", "time_watch.projecte.mode": "Time control mode: %s", "time_watch.projecte.mode_switch": "Time control mode set to: %s", "time_watch.projecte.off": "Off", "time_watch.projecte.rewind": "Rewind", "tooltip.projecte.arcana.inactive": "Inactive!", "tooltip.projecte.evertide.1": "Press %s to fire a water projectile", "tooltip.projecte.evertide.2": "Acts as an infinite water bucket", "tooltip.projecte.evertide.3": "Right click to fill tanks and cauldrons", "tooltip.projecte.evertide.4": "All operations are completely free!", "tooltip.projecte.gem_density.1": "Condenses items on the go", "tooltip.projecte.gem_density.2": "Current target: %s", "tooltip.projecte.gem_density.3": "Press %s to change target", "tooltip.projecte.gem_density.4": "Right click to set up blacklist/whitelist", "tooltip.projecte.gem_density.5": "Shift right click to toggle", "tooltip.projecte.philostone": "Press %s to open a crafting grid", "tooltip.projecte.stored_xp": "Stored XP: %s", "tooltip.projecte.time_watch.1": "Become the master of time", "tooltip.projecte.time_watch.2": "Right click to change mode", "tooltip.projecte.tome": "Unlocks all transmutation knowledge when learned", "tooltip.projecte.volcanite.1": "Press %s to fire a lava projectile", "tooltip.projecte.volcanite.2": "Acts as infinitely full lava bucket", "tooltip.projecte.volcanite.3": "Right click to fill tanks and cauldrons", "tooltip.projecte.volcanite.4": "All operations cost 32 EMC!", "transmutation.projecte.learned.1": "L", "transmutation.projecte.learned.2": "e", "transmutation.projecte.learned.3": "a", "transmutation.projecte.learned.4": "r", "transmutation.projecte.learned.5": "n", "transmutation.projecte.learned.6": "e", "transmutation.projecte.learned.7": "d", "transmutation.projecte.learned.8": "!", "transmutation.projecte.transmute": "Transmutation", "transmutation.projecte.unlearned.1": "U", "transmutation.projecte.unlearned.2": "n", "transmutation.projecte.unlearned.3": "l", "transmutation.projecte.unlearned.4": "e", "transmutation.projecte.unlearned.5": "a", "transmutation.projecte.unlearned.6": "r", "transmutation.projecte.unlearned.7": "n", "transmutation.projecte.unlearned.8": "e", "transmutation.projecte.unlearned.9": "d"}