package net.prizowo.emcshare.mixin;

import moze_intel.projecte.gameObjs.container.inventory.TransmutationInventory;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.entity.player.Player;
import net.prizowo.emcshare.capability.SharedEMCCapability;
import net.prizowo.emcshare.data.EMCTeam;
import net.prizowo.emcshare.data.EMCTeamManager;
import net.prizowo.emcshare.network.PacketHandler;
import net.prizowo.emcshare.network.packet.SyncSharedEMCPacket;
import org.spongepowered.asm.mixin.Final;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.Shadow;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfoReturnable;

import java.math.BigInteger;

@Mixin(value = TransmutationInventory.class, remap = false)
public class TransmutationInventoryMixin {

    @Shadow @Final private Player player;

    @Inject(method = "getAvailableEmc", at = @At("HEAD"), cancellable = true)
    private void onGetAvailableEmc(CallbackInfoReturnable<BigInteger> cir) {
        // Check if player is using shared EMC
        player.getCapability(SharedEMCCapability.SHARED_EMC_CAPABILITY).ifPresent(cap -> {
            if (cap.isUsingSharedEMC()) {
                BigInteger sharedEMC = cap.getAvailableEMC(player);
                cir.setReturnValue(sharedEMC);
            }
        });
    }

    @Inject(method = "addEmc", at = @At("HEAD"), cancellable = true)
    private void onAddEmc(BigInteger value, CallbackInfo ci) {
        player.getCapability(SharedEMCCapability.SHARED_EMC_CAPABILITY).ifPresent(cap -> {
            if (cap.isUsingSharedEMC()) {
                cap.addEMC(player, value);
                // Sync immediately after adding EMC
                syncToTeamMembers();
                ci.cancel(); // Prevent original method execution
            }
        });
    }

    @Inject(method = "removeEmc", at = @At("HEAD"), cancellable = true)
    private void onRemoveEmc(BigInteger value, CallbackInfo ci) {
        player.getCapability(SharedEMCCapability.SHARED_EMC_CAPABILITY).ifPresent(cap -> {
            if (cap.isUsingSharedEMC()) {
                cap.consumeEMC(player, value);
                // Sync immediately after consuming EMC
                syncToTeamMembers();
                ci.cancel(); // Prevent original method execution
            }
        });
    }

    private void syncToTeamMembers() {
        if (!(player instanceof ServerPlayer serverPlayer)) return;

        EMCTeamManager manager = EMCTeamManager.getInstance();
        if (manager == null) return;

        EMCTeam team = manager.getPlayerTeam(player.getUUID());
        if (team == null) return;

        player.getCapability(SharedEMCCapability.SHARED_EMC_CAPABILITY).ifPresent(cap -> {
            BigInteger totalEMC = cap.getAvailableEMC(player);
            SyncSharedEMCPacket packet = new SyncSharedEMCPacket(true, totalEMC, team.getTeamName(), team.getMemberCount());

            // Send to all online team members
            try {
                MinecraftServer server = serverPlayer.getServer();
                if (server != null) {
                    for (java.util.UUID memberId : team.getMembers()) {
                        ServerPlayer member = server.getPlayerList().getPlayer(memberId);
                        if (member != null) {
                            PacketHandler.sendToPlayer(packet, member);
                        }
                    }
                }
            } catch (Exception e) {
                net.prizowo.emcshare.EMCShareMod.LOGGER.error("Failed to sync EMC to team members", e);
            }
        });
    }
}
