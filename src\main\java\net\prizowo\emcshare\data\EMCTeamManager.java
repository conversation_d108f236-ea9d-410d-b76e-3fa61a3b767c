package net.prizowo.emcshare.data;

import net.minecraft.nbt.CompoundTag;
import net.minecraft.nbt.ListTag;
import net.minecraft.nbt.Tag;
import net.minecraft.server.MinecraftServer;

import net.minecraft.world.level.saveddata.SavedData;
import net.minecraft.world.level.storage.DimensionDataStorage;
import net.prizowo.emcshare.EMCShareMod;

import javax.annotation.Nullable;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

public class EMCTeamManager extends SavedData {
    private static final String DATA_NAME = "emc_teams";
    private static EMCTeamManager instance;
    
    private final Map<String, EMCTeam> teams = new ConcurrentHashMap<>();
    private final Map<UUID, String> playerToTeam = new ConcurrentHashMap<>();

    public EMCTeamManager() {
        super();
    }

    public EMCTeamManager(CompoundTag nbt) {
        this();
        
        ListTag teamsList = nbt.getList("teams", Tag.TAG_COMPOUND);
        for (int i = 0; i < teamsList.size(); i++) {
            CompoundTag teamNbt = teamsList.getCompound(i);
            EMCTeam team = new EMCTeam(teamNbt);
            teams.put(team.getTeamId(), team);
            
            // Build player to team mapping
            for (UUID member : team.getMembers()) {
                playerToTeam.put(member, team.getTeamId());
            }
        }
    }

    @Override
    public CompoundTag save(CompoundTag nbt) {
        ListTag teamsList = new ListTag();
        for (EMCTeam team : teams.values()) {
            teamsList.add(team.serializeNBT());
        }
        nbt.put("teams", teamsList);
        return nbt;
    }

    public static EMCTeamManager getInstance() {
        return instance;
    }

    public static void loadTeams(MinecraftServer server) {
        DimensionDataStorage storage = server.overworld().getDataStorage();
        instance = storage.computeIfAbsent(EMCTeamManager::new, EMCTeamManager::new, DATA_NAME);
    }

    public static void saveTeams() {
        if (instance != null) {
            instance.setDirty();
        }
    }

    public EMCTeam createTeam(String teamName, UUID owner) {
        String teamId = UUID.randomUUID().toString();
        
        // Remove player from existing team
        leaveTeam(owner);
        
        EMCTeam team = new EMCTeam(teamId, teamName, owner);
        teams.put(teamId, team);
        playerToTeam.put(owner, teamId);
        
        setDirty();
        EMCShareMod.LOGGER.info("Created team {} with owner {}", teamName, owner);
        return team;
    }

    public boolean deleteTeam(String teamId, UUID requesterId) {
        EMCTeam team = teams.get(teamId);
        if (team == null || !team.isOwner(requesterId)) {
            return false;
        }
        
        // Remove all members from mapping
        for (UUID member : team.getMembers()) {
            playerToTeam.remove(member);
        }
        
        teams.remove(teamId);
        setDirty();
        EMCShareMod.LOGGER.info("Deleted team {} by {}", team.getTeamName(), requesterId);
        return true;
    }

    public boolean joinTeam(String teamId, UUID playerId) {
        EMCTeam team = teams.get(teamId);
        if (team == null) {
            return false;
        }
        
        // Remove from existing team
        leaveTeam(playerId);
        
        if (team.addMember(playerId)) {
            playerToTeam.put(playerId, teamId);
            setDirty();
            EMCShareMod.LOGGER.info("Player {} joined team {}", playerId, team.getTeamName());

            // Sync EMC for all team members
            syncTeamEMC(team);
            return true;
        }
        return false;
    }

    public boolean leaveTeam(UUID playerId) {
        String currentTeamId = playerToTeam.get(playerId);
        if (currentTeamId == null) {
            return false;
        }
        
        EMCTeam team = teams.get(currentTeamId);
        if (team == null) {
            playerToTeam.remove(playerId);
            return false;
        }
        
        if (team.isOwner(playerId)) {
            // Transfer ownership or delete team
            Set<UUID> members = team.getMembers();
            members.remove(playerId);
            
            if (members.isEmpty()) {
                // Delete team if no other members
                deleteTeam(currentTeamId, playerId);
            } else {
                // Transfer ownership to first member
                UUID newOwner = members.iterator().next();
                team.setOwner(newOwner);
                team.removeMember(playerId);
                playerToTeam.remove(playerId);
                setDirty();
                EMCShareMod.LOGGER.info("Transferred team {} ownership from {} to {}", 
                    team.getTeamName(), playerId, newOwner);
            }
        } else {
            team.removeMember(playerId);
            playerToTeam.remove(playerId);
            setDirty();
            EMCShareMod.LOGGER.info("Player {} left team {}", playerId, team.getTeamName());

            // Sync EMC for remaining team members
            syncTeamEMC(team);
        }
        
        return true;
    }

    @Nullable
    public EMCTeam getPlayerTeam(UUID playerId) {
        String teamId = playerToTeam.get(playerId);
        return teamId != null ? teams.get(teamId) : null;
    }

    @Nullable
    public EMCTeam getTeam(String teamId) {
        return teams.get(teamId);
    }

    public Collection<EMCTeam> getAllTeams() {
        return new ArrayList<>(teams.values());
    }

    public boolean isPlayerInTeam(UUID playerId) {
        return playerToTeam.containsKey(playerId);
    }

    /**
     * Sync EMC data for all online members of a team
     */
    private void syncTeamEMC(EMCTeam team) {
        try {
            net.minecraft.server.MinecraftServer server = net.minecraftforge.server.ServerLifecycleHooks.getCurrentServer();
            if (server != null) {
                for (UUID memberId : team.getMembers()) {
                    net.minecraft.server.level.ServerPlayer player = server.getPlayerList().getPlayer(memberId);
                    if (player != null) {
                        // Use the event handler's sync method
                        net.prizowo.emcshare.event.EMCShareEventHandler.forceSyncPlayerEMC(player);
                    }
                }
            }
        } catch (Exception e) {
            EMCShareMod.LOGGER.error("Failed to sync team EMC", e);
        }
    }
}
