package net.prizowo.emcshare.network.packet;

import net.minecraft.client.Minecraft;
import net.minecraft.network.FriendlyByteBuf;
import net.minecraft.world.entity.player.Player;
import net.minecraftforge.network.NetworkEvent;
import net.prizowo.emcshare.capability.SharedEMCCapability;
import net.prizowo.emcshare.client.ClientEMCData;

import java.math.BigInteger;
import java.util.function.Supplier;

public class SyncSharedEMCPacket {
    private final boolean useSharedEMC;
    private final BigInteger availableEMC;
    private final String teamName;
    private final int memberCount;

    public SyncSharedEMCPacket(boolean useSharedEMC, BigInteger availableEMC, String teamName, int memberCount) {
        this.useSharedEMC = useSharedEMC;
        this.availableEMC = availableEMC;
        this.teamName = teamName;
        this.memberCount = memberCount;
    }

    public static void encode(SyncSharedEMCPacket packet, FriendlyByteBuf buffer) {
        buffer.writeBoolean(packet.useSharedEMC);
        buffer.writeUtf(packet.availableEMC.toString());
        buffer.writeUtf(packet.teamName);
        buffer.writeInt(packet.memberCount);
    }

    public static SyncSharedEMCPacket decode(FriendlyByteBuf buffer) {
        boolean useSharedEMC = buffer.readBoolean();
        BigInteger availableEMC = new BigInteger(buffer.readUtf());
        String teamName = buffer.readUtf();
        int memberCount = buffer.readInt();
        return new SyncSharedEMCPacket(useSharedEMC, availableEMC, teamName, memberCount);
    }

    public static void handle(SyncSharedEMCPacket packet, Supplier<NetworkEvent.Context> contextSupplier) {
        NetworkEvent.Context context = contextSupplier.get();
        context.enqueueWork(() -> {
            Player player = Minecraft.getInstance().player;
            if (player != null) {
                // Update capability first
                player.getCapability(SharedEMCCapability.SHARED_EMC_CAPABILITY).ifPresent(cap -> {
                    cap.setUsingSharedEMC(packet.useSharedEMC);
                });

                // Update client-side data for display
                ClientEMCData.updateSharedEMC(packet.useSharedEMC, packet.availableEMC, packet.teamName, packet.memberCount);

                // Force refresh any open transmutation GUI
                if (player.containerMenu != null) {
                    player.containerMenu.broadcastChanges();
                }
            }
        });
        context.setPacketHandled(true);
    }
}
